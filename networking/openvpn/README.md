# VPN Module

## Overview

This code is thinking to build an Openvpn instance using Packer/Ansible to install and deploy the configuration and Terraform to build the instance.

So, we have the next tools and version to run this startup

- **packer=>1.7.5**: Create the AMIs
- **terraform=>1.0.1**: Create the ec2 instance with eip
- **aws=>3.53.0**: Cloud provider where infrastructure exists.
- **ansible=>2.11.5**: Install OpenVPN and deploy the code.


## Usage

### Default configuration

Default configuration is located at `packer/ansible/vpn/roles/common/defaults/main.yml`

### Build image

```
cd packer
packer build .
```

### Deploy resources

#### Variables:
##### Required
- **ami**: Image ID generated by packer
- **vpc**: VPC to launch the resources in
- **vpc_zone_identifier**: Subnet to launch the resources in
- **key_pair**: Key name to be used for the instance

##### Optional
- **vpn_protocol**: Public OpenVPN port
- **vpn_port**: OpenVPN protocol
- **source_ssh_cidr**: List of cidr to allow ssh from

#### Run terraform
```
terraform apply
```
#### Output
- **vpn_server_ip**: VPN server public IP

### Generate client certificates
- SSH to the server using the key previously indicated
- Edit the file `/etc/openvpn/client_conf/client.conf.example`, modify the remote line `remote <vpn_server_ip> <vpn_server_port>`
- Create cliente certificate `create-creds <username>`

Path of the client credentials `/etc/openvpn/client_conf/<username>.ovpn`

You can get the client credentials from the server using scp.

Connect to the server using a cliente. Eg: `openvpn <username>.ovpn`
