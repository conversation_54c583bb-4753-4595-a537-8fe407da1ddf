resource "aws_eip" "eip" {
  vpc = true

  tags = var.tags
}

resource "aws_lb" "lb" {
  load_balancer_type = "network"

  subnet_mapping {
    subnet_id     = var.lb_subnets_ids[0]
    allocation_id = aws_eip.eip.id
  }

  tags = var.tags
}

resource "aws_lb_target_group" "ssh_tg" {
   port        = var.ssh_port
   protocol    = "TCP"
   vpc_id      = var.vpc
   target_type = "instance"

   health_check {
     port     = "traffic-port"
     protocol = "TCP"
   }
 }

resource "aws_lb_target_group" "vpn_tg" {
  port        = var.vpn_port
  protocol    = var.vpn_protocol
  vpc_id      = var.vpc
  target_type = "instance"

  health_check {
    port     = var.vpn_protocol == "UDP" ? "1194" : "22"
    protocol = "TCP"
  }

  tags = var.tags

}

resource "aws_lb_listener" "ssh_listener" {
   default_action {
     target_group_arn = aws_lb_target_group.ssh_tg.arn
     type             = "forward"
   }

   load_balancer_arn = aws_lb.lb.arn
   port              = var.ssh_port
   protocol          = "TCP"
}

resource "aws_lb_listener" "vpn_listener" {
  default_action {
    target_group_arn = aws_lb_target_group.vpn_tg.arn
    type             = "forward"
  }

  load_balancer_arn = aws_lb.lb.arn
  port              = var.vpn_port
  protocol          = var.vpn_protocol

  tags = var.tags
}

resource "aws_security_group" "sg" {
  description = "Enable VPN access" # "Enable SSH/VPN access"

  vpc_id = var.vpc

  tags = var.tags
}

resource "aws_security_group_rule" "out" {
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "all"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.sg.id
}

resource "aws_security_group_rule" "ssh" {
   type              = "ingress"
   from_port         = var.ssh_port
   to_port           = var.ssh_port
   protocol          = "TCP"
   cidr_blocks       = var.source_ssh_cidr
   security_group_id = aws_security_group.sg.id
}

resource "aws_security_group_rule" "vpn" {
  type              = "ingress"
  from_port         = var.vpn_port
  to_port           = var.vpn_port
  protocol          = var.vpn_protocol
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.sg.id
}

resource "aws_launch_template" "lt" {
  name_prefix   = "openvpn"
  image_id      = var.ami
  instance_type = var.instance_type

  key_name = var.key_pair

  network_interfaces {
    associate_public_ip_address = var.associate_public_ip_address
    security_groups             = [aws_security_group.sg.id]
    delete_on_termination       = true
  }


  update_default_version = true

  tag_specifications {
    resource_type = "instance"

    tags = (merge(var.tags,
      {
        Name = "openvpn"
      }
    ))
  }

 # iam_instance_profile {
 #   name = var.instance_profile
 # }

  tags = var.tags
}

locals {
  tag_list = [for k, v in var.tags : {
    "key"                 = k
    "value"               = v
    "propagate_at_launch" = true
  }]
}

resource "aws_autoscaling_group" "as" {
  vpc_zone_identifier       = var.asg_vpc_zone_identifier
  desired_capacity          = var.desired_capacity
  max_size                  = var.max_size
  min_size                  = var.min_size
  default_cooldown          = 180
  health_check_grace_period = 180
  health_check_type         = "EC2"

  target_group_arns =  [aws_lb_target_group.ssh_tg.arn, aws_lb_target_group.vpn_tg.arn]

  launch_template {
    id      = aws_launch_template.lt.id
    version = "$Latest"
  }

  tags = local.tag_list
}

resource "aws_autoscaling_policy" "policy" {
  name                   = "WPASGPolicy"
  policy_type            = "TargetTrackingScaling"
  autoscaling_group_name = aws_autoscaling_group.as.name

  target_tracking_configuration {
    predefined_metric_specification {
      predefined_metric_type = var.target_metric
    }

    target_value    = var.target_metric_value
  }
}
