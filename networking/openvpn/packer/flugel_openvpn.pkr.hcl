variable "region" {
  type    = string
  default = "us-east-1"
}

variable "instance_type" {
  type    = string
  default = "t2.micro"
}

data "amazon-ami" "ubuntu" {
  filters = {
    name                = "ubuntu/images/*ubuntu-focal-20.04-amd64-server-*"
    root-device-type    = "ebs"
    virtualization-type = "hvm"
  }
  most_recent = true
  owners      = ["099720109477"]
  region      = "${var.region}"
}

locals { timestamp = regex_replace(timestamp(), "[- TZ:]", "") }

source "amazon-ebs" "ubuntu" {
  ami_name      = "flugel_openvpn_${local.timestamp}"
  instance_type = "${var.instance_type}"
  region        = "${var.region}"
  source_ami    = "${data.amazon-ami.ubuntu.id}"
  ssh_username  = "ubuntu"

#  vpc_id            = "vpc-0bb8ab96d24dd1170"
#  subnet_id         = "subnet-0366d987ede1ec29f"
#  security_group_id = "sg-06587655ceb97710a"

}

build {
  sources = ["source.amazon-ebs.ubuntu"]

  provisioner "ansible" {
    ansible_env_vars = [ "ANSIBLE_HOST_KEY_CHECKING=False", "ANSIBLE_SSH_ARGS='-o ForwardAgent=yes -o ControlMaster=auto -o ControlPersist=60s'" ]
    extra_arguments = [ "--scp-extra-args", "'-O'" ]
    ansible_ssh_extra_args = ["-o HostKeyAlgorithms=+ssh-rsa -o PubkeyAcceptedKeyTypes=+ssh-rsa"]
    playbook_file    = "./ansible/vpn/playbook.yml"
  }

}

