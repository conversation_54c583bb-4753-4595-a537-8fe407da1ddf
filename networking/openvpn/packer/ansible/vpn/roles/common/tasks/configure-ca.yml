- name: Create CA dir
  command: make-cadir /etc/openvpn/ca
  args:
    creates: /etc/openvpn/ca

- name: Configure CA vars
  lineinfile:
    path: /etc/openvpn/ca/vars
    state: present
    line: "{{ item }}"
  loop:
    - 'set_var EASYRSA_BATCH          "1"'
    - 'set_var EASYRSA_REQ_CN         "{{ servername }}"'
    - 'set_var EASYRSA_REQ_COUNTRY    "{{ easyrsa_req_country }}"'
    - 'set_var EASYRSA_REQ_PROVINCE   "{{ easyrsa_req_province }}"'
    - 'set_var EASYRSA_REQ_CITY       "{{ easyrsa_req_city }}"'
    - 'set_var EASYRSA_REQ_ORG        "{{ easyrsa_req_org }}"'
    - 'set_var EASYRSA_REQ_EMAIL      "{{ easyrsa_req_email }}"'
    - 'set_var EASYRSA_REQ_OU         "{{ easyrsa_req_ou }}"'

- name: Init PKI 
  command: ./easyrsa init-pki
  args:
    creates: /etc/openvpn/ca/pki
    chdir: /etc/openvpn/ca

- name: Generate random file 
  command: dd if=/dev/urandom of=pki/.rnd bs=256 count=1
  args:
    creates: /etc/openvpn/ca/pki/.rnd
    chdir: /etc/openvpn/ca

- name: Build CA 
  command: ./easyrsa build-ca nopass
  args:
    creates: /etc/openvpn/ca/pki/ca.crt
    chdir: /etc/openvpn/ca

- name: Generate server key 
  command: ./easyrsa gen-req {{ servername }} nopass
  args:
    creates: /etc/openvpn/ca/pki/private/{{ servername }}.key
    chdir: /etc/openvpn/ca

- name: Generate DH parameters
  command: ./easyrsa gen-dh
  args:
    creates: /etc/openvpn/ca/pki/dh.pem
    chdir: /etc/openvpn/ca

- name: Generate server key
  command: ./easyrsa sign-req server {{ servername }}
  args:
    creates: /etc/openvpn/ca/pki/issued/{{ servername }}.crt
    chdir: /etc/openvpn/ca

- name: Copy cert files
  copy:
    force: no
    remote_src: yes
    src: '{{item}}'
    dest: '/etc/openvpn/'
  loop:
    - /etc/openvpn/ca/pki/dh.pem
    - /etc/openvpn/ca/pki/ca.crt
    - /etc/openvpn/ca/pki/issued/{{ servername }}.crt
    - /etc/openvpn/ca/pki/private/{{ servername }}.key

