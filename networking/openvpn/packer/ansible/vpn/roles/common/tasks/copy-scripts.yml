- name: Copy list-valid-creds script
  copy: #src=list-valid-creds dest=/usr/local/sbin/list-valid-creds owner=root group=root mode=0755
    src: list-valid-creds
    dest: /usr/local/sbin/list-valid-creds
    owner: root
    group: root
    mode: '0755'
  become: yes

- name: Copy create-creds script
  copy: #src=create-creds dest=/usr/local/sbin/create-creds owner=root group=root mode=0755
    src: create-creds
    dest: /usr/local/sbin/create-creds
    owner: root
    group: root
    mode: '0755'
  become: yes

- name: Copy make_config script
  copy: #src=make_config dest=/usr/local/sbin/make_config owner=root group=root mode=0755
    src: make_config
    dest: /usr/local/sbin/make_config
    owner: root
    group: root
    mode: '0755'
  become: yes

- name: Copy revoke-creds script
  copy: #src=revoke-creds dest=/usr/local/sbin/revoke-creds owner=root group=root mode=0755
    src: revoke-creds
    dest: /usr/local/sbin/revoke-creds
    owner: root
    group: root
    mode: '0755'
  become: yes

- name: Copy backup-openvpn script
  copy: 
    src: backup-openvpn
    dest: /usr/local/sbin/backup-openvpn
    owner: root
    group: root
    mode: '0755'
  become: yes
