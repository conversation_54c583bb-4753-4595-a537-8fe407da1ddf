- name: Copy server template conf
  copy:
    src: /usr/share/doc/openvpn/examples/sample-config-files/server.conf.gz
    dest: /etc/openvpn
    remote_src: yes
    force: no

- name: Decompress server template conf
  command: gzip -d server.conf.gz
  args:
    chdir: /etc/openvpn
    creates: /etc/openvpn/server.conf

- name: Edit server conf
  replace:
    path: /etc/openvpn/server.conf
    regexp: '{{ item.regexp }}'
    replace: '{{ item.replace }}'
  loop:
    - { regexp: '^port .+', replace: 'port {{ openvpn_port }}'}
    - { regexp: '^proto .+', replace: 'proto {{ openvpn_proto }}'}
    - { regexp: '^ca .+\.crt', replace: 'ca /etc/openvpn/ca.crt'}
    - { regexp: '^cert .+\.crt', replace: 'cert /etc/openvpn/{{ servername }}.crt'}
    - { regexp: '^key .+\.key', replace: 'key /etc/openvpn/{{ servername }}.key'}
    - { regexp: '^dh .+\.pem', replace: 'dh /etc/openvpn/dh.pem'}
    - { regexp: '^dh .+\.pem', replace: 'dh /etc/openvpn/dh.pem'}

- name: Add server options
  lineinfile:
    path: /etc/openvpn/server.conf
    state: present
    line: "{{ item }}"
  loop: "{{ openvpn_server_options }}"

- name: Create client template folder
  file:
    path: /etc/openvpn/client_conf
    state: directory
    mode: '0755'

- name: Copy client template conf
  copy:
    src: client.conf.example
    dest: /etc/openvpn/client_conf/client.conf.example
    force: no

- name: Generate TA key
  command: openvpn --genkey --secret ta.key
  args:
    chdir: /etc/openvpn
    creates: /etc/openvpn/ta.key

- name: Configure to forward packages
  sysctl: #name=net.ipv4.ip_forward value=1 state=present
    name: net.ipv4.ip_forward
    value: '1'
    state: present

- name: Masquerading connections
  iptables: #table=nat chain=POSTROUTING out_interface=eth0 jump=MASQUERADE
    table: nat
    chain: POSTROUTING
    out_interface: ens5 #for ec2 t3 family, this needs to be set to ens5. For t2 family, eth0
    jump: MASQUERADE

- name: Persist iptables rules
  shell: iptables-save > /etc/iptables/rules.v4
  become: yes

- name: Verify iptables-save saved state in the file
  command: cat /etc/iptables/rules.v4
  register: output_ipts

- name: show output of iptables-save from file
  debug:
    var: output_ipts.stdout_lines
