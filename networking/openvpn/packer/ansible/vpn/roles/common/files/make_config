#!/bin/bash

BASE_DIR=/etc/openvpn
OUTPUT_DIR=/etc/openvpn/client_conf

cat ${BASE_DIR}/client_conf/client.conf.example \
    <(echo -e '<ca>') \
    ${BASE_DIR}/ca.crt \
    <(echo -e '</ca>\n<cert>') \
    ${BASE_DIR}/ca/pki/issued/${1}.crt \
    <(echo -e '</cert>\n<key>') \
    ${BASE_DIR}/ca/pki/private/${1}.key \
    <(echo -e '</key>\n<tls-auth>') \
    ${BASE_DIR}/ta.key \
    <(echo -e '</tls-auth>') \
    > ${OUTPUT_DIR}/${1}.ovpn
