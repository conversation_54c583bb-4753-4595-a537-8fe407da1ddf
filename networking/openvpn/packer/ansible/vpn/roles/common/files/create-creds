#!/bin/bash

if [ $# -eq 0 ]
  then
    echo -e "\nPlease, inform a credential name\n"
    exit -1
fi

cd /etc/openvpn/ca
./easyrsa build-client-full ${1} nopass

make_config ${1}

#backup-openvpn

#echo -e "\nYour new credential file is in /etc/openvpn/client_conf/${1}.ovpn\n"

#aws s3 cp /etc/openvpn/client_conf/${1}.ovpn s3://example-vpn-credentials/${1}.ovpn

#echo -e "\nYour new credential file has been uploaded to s3://example-vpn-credentials/${1}.ovpn\n"
