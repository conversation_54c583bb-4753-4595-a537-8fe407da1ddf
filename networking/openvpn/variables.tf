variable "region" {
  description = "AWS region."
  type        = string
  default     = "us-east-1"
}

variable "tags" {
  description = "A map of tags to add to resources"
  type        = map(string)
  default     = {}
}

variable "ami" {
  description = "The AMI from which to launch the instance."
  type        = string
}

variable "desired_capacity" {
  description = "The desired capacity of the ASG."
  type        = number
  default     = 1
}

variable "min_size" {
  description = "The minimum capacity of the ASG."
  type        = number
  default     = 1
}

variable "max_size" {
  description = "The maximum capacity of the ASG."
  type        = number
  default     = 1
}

variable "instance_type" {
  description = "The type of the instance."
  type        = string
  default     = "t2.micro"
}

variable "target_metric" {
  description = "Target tracking metric"
  type        = string
  default     = "ASGAverageCPUUtilization"
}

variable "target_metric_value" {
  description = "Target value for the metric"
  type        = string
  default     = "70.0"
}

variable "lb_subnets_ids" {
  description = "A list of subnet IDs to attach to LB."
  type        = list(string)
}

variable "asg_vpc_zone_identifier" {
  description = "A list of subnet IDs to launch resources in."
  type        = list(string)
}

variable "vpc" {
  description = "VPC ID"
  type        = string
}

variable "ssh_port" {
   description = "Public SSH port"
   type        = string
   default     = "22"
}

variable "vpn_port" {
  description = "Public OpenVPN port"
  type        = string
  default     = "20235"
}

variable "vpn_protocol" {
  description = "OpenVPN protocol"
  type        = string
  default     = "UDP"
}

variable "key_pair" {
  description = "The key name to use for the instance."
  type        = string
}

variable "source_ssh_cidr" {
   description = "List of cidr to allow ssh from"
   type        = list(string)
   default     = ["0.0.0.0/0"]
}

#variable "instance_profile" {
#  description = "The name of the instance profile."
#  type        = string
#  default     = "SSMInstanceProfile"
#}

variable "associate_public_ip_address" {
  default = true
}
