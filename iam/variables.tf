variable "region" {
  description = "AWS region of which the resources will be deployed."
  type        = string
  default     = "us-east-1"
}

variable "security_audit_policy_arn" {
  description = "Security Audit IAM policy ARN (required for Prowler Scanning)."
  type        = string
  default     = "arn:aws:iam::aws:policy/SecurityAudit"
}

variable "view_only_access_policy_arn" {
  description = "View Only Access IAM policy ARN (required for Prowler Scanning)."
  type        = string
  default     = "arn:aws:iam::aws:policy/job-function/ViewOnlyAccess"
}

variable "prowler_additions_policy_name" {
  description = "The name of the Prowler additionals policy."
  type        = string
  default     = "ProwlerAdditionsPolicy"
}

variable "prowler_scanning_eks_sa_role_name" {
  description = "The name of the Prowler Scanning role for the EKS Service Account."
  type        = string
  default     = "ProwlerScanningEKSServiceAccountRole"
}

variable "eks_cluster_name" {
  description = "The name of the EKS Cluster."
  type        = string
  default     = "batuta-cluster"
}

variable "eks_cluster_namespace" {
  description = "The name of the default EKS Cluster Namespace."
  type        = string
  default     = "default"
}

variable "eks_cluster_service_account" {
  description = "The name of the default EKS Cluster Service Account."
  type        = string
  default     = "default"
}
