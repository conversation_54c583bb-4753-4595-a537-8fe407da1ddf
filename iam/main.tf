locals {
  prowler_additions_policy = file("${path.module}/json/prowler-additions-policy.json")
}

module "iam_policy" {
  source = "terraform-aws-modules/iam/aws//modules/iam-policy"

  name   = var.prowler_additions_policy_name
  policy = local.prowler_additions_policy
}

module "iam_eks_role" {
  source = "terraform-aws-modules/iam/aws//modules/iam-eks-role"

  create_role = true
  role_name   = var.prowler_scanning_eks_sa_role_name

  cluster_service_accounts = {
    (var.eks_cluster_name) = [
        "${var.eks_cluster_namespace}:${var.eks_cluster_service_account}"
    ]
  }

  role_policy_arns = {
    SecurityAudit          = var.security_audit_policy_arn,
    ViewOnlyAccess         = var.view_only_access_policy_arn,
    ProwlerAdditionsPolicy = module.iam_policy.arn,
  }
}
