{"Version": "2012-10-17", "Statement": [{"Action": ["account:Get*", "appstream:Describe*", "appstream:List*", "backup:List*", "cloudtrail:GetInsightSelectors", "codeartifact:List*", "codebuild:BatchGet*", "drs:Describe*", "ds:Get*", "ds:Describe*", "ds:List*", "ec2:GetEbsEncryptionByDefault", "ecr:Describe*", "ecr:GetRegistryScanningConfiguration", "elasticfilesystem:DescribeBackupPolicy", "glue:GetConnections", "glue:GetSecurityConfiguration*", "glue:SearchTables", "lambda:GetFunction*", "logs:FilterLogEvents", "macie2:GetMacieSession", "s3:GetAccountPublicAccessBlock", "s3:PutObject", "shield:DescribeProtection", "shield:GetSubscriptionState", "securityhub:BatchImportFindings", "securityhub:GetFindings", "ssm:GetDocument", "ssm-incidents:List*", "support:Describe*", "tag:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "Resource": "*", "Effect": "Allow"}, {"Effect": "Allow", "Action": ["apigateway:GET"], "Resource": ["arn:aws:apigateway:*::/restapis/*", "arn:aws:apigateway:*::/apis/*"]}]}