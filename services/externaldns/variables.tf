variable "cluster_node_role_arn" {
  description = "(Required) Cluster Node Role ARN"
  type        = string
}

variable "external_dns_role_name" {
  description = "Name of the External DNS Role"
  type        = string
  default     = "external-dns-role"
}

variable "external_dns_policy_name" {
  description = "Name of the External DNS Policy"
  type        = string
  default     = "external-dns-policy"
}

variable "aws_account_id" {
  description = "(Required) AWS Account ID"
  type        = string
  default     = ""
}

variable "oidc_provider_eks" {
  description = "(Required) OIDC Provider EKS"
  type        = string
  default     = ""
}

variable "external_dns_namespace" {
  description = "External DNS Namespace"
  type        = string
  default     = "external-dns"
}

variable "external_dns_serviceaccount" {
  description = "External DNS Service Account"
  type        = string
  default     = "external-dns"
}
