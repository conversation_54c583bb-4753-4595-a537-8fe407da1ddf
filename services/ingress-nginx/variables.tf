variable "namespace" {
  type        = string
  description = "The Kubernetes namespace where the ingress-nginx will be deployed."
}

variable "repository" {
  type        = string
  description = "The ingress-nginx chart repository."
  default     = "https://kubernetes.github.io/ingress-nginx"
}


variable "name" {
  type        = string
  description = "The ingress-nginx chart name to deploy."
  default     = "ingress-nginx"
}

variable "chart" {
  type        = string
  description = "The ingress-nginx chart to deploy."
  default     = "ingress-nginx"
}

variable "chart_version" {
  type        = string
  description = "The version of the ingress-nginx chart to deploy."
  default     = "4.0.8"
}

variable "values" {
  type        = map(any)
  description = "A map of values to pass to the ingress-nginx chart."
  default     = {}
}

variable "kubeconfig" {
  description = "Path to your kube config."
  type        = string
  default     = "~/.kube/config"
}
