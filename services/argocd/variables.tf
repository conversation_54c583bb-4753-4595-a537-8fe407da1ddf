variable "region" {
  description = "The AWS region to deploy resources"
  type        = string
  default     = "us-east-1"
}

variable "kubernetes_config_path" {
  description = "The path to the kubernetes config"
  type        = string
  default     = "~/.kube/config"
}

variable "eks_node_group_role_name" {
  description = "The IAM role used by the EKS worker node. This role is granted read access to Amazon ECR and AWS KMS (i.e. '00000-dev-cluster00000000000000000000000000')"
  type        = string
  default     = ""
}

variable "ecr_image_tag" {
  description = "The tag for the ArgoCD docker image"
  type        = string
  default     = "latest"
}

variable "eks_cluster_name" {
  type = string
}
