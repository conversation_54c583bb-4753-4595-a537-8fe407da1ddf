data "aws_caller_identity" "current" {}

data "aws_iam_role" "aws_eks_node_group_role" {
  count = var.role_name != "" ? 1 : 0
  name  = var.role_name
}

data "aws_iam_policy_document" "key_policy" {
  statement {
    actions = [
      "kms:Create*",
      "kms:Describe*",
      "kms:Enable*",
      "kms:List*",
      "kms:Put*",
      "kms:Update*",
      "kms:Revoke*",
      "kms:Disable*",
      "kms:Get*",
      "kms:Delete*",
      "kms:TagResource",
      "kms:UntagResource",
      "kms:ScheduleKeyDeletion",
      "kms:CancelKeyDeletion"
    ]

    # users and roles who can manage the key
    principals {
      type        = "AWS"
      identifiers = [data.aws_caller_identity.current.arn]
    }

    resources = ["*"]
  }

  statement {
    actions = [
      "kms:Encrypt",
      "kms:Decrypt",
      "kms:ReEncrypt*",
      "kms:GenerateDataKey*",
      "kms:DescribeKey"
    ]

    # users and roles who can use the key
    principals {
      type        = "AWS"
      identifiers = length(data.aws_iam_role.aws_eks_node_group_role) == 1 ? [data.aws_iam_role.aws_eks_node_group_role[0].arn, data.aws_caller_identity.current.arn] : [data.aws_caller_identity.current.arn]
    }

    resources = ["*"]
  }
}

resource "aws_kms_key" "key" {
  enable_key_rotation = true
  policy              = data.aws_iam_policy_document.key_policy.json
}