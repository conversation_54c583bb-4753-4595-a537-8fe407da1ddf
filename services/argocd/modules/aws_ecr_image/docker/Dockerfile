FROM viaductoss/ksops:v3.0.2 as ksops

FROM argoproj/argocd:v2.4.6

# Dockerfile template based off https://itnext.io/argocd-a-helm-chart-deployment-and-working-with-helm-secrets-via-aws-kms-96509bfc5eb3

USER root

COPY helm-wrapper.sh /usr/local/bin/

RUN apt-get update && \
    apt-get install -y curl && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* && \
    curl -o /usr/local/bin/sops -L https://github.com/mozilla/sops/releases/download/v3.7.1/sops-v3.7.1.linux && \
    chmod +x /usr/local/bin/sops && \
    cd /usr/local/bin && \
    mv helm helm.bin && \
    mv helm-wrapper.sh helm && \
    chmod +x helm 

ENV XDG_CONFIG_HOME=$HOME/.config
ENV XDG_CONFIG_HOME=/home/<USER>/.config
ENV KUSTOMIZE_PLUGIN_PATH=$XDG_CONFIG_HOME/kustomize/plugin/

# Override the default kustomize executable with the Go built version
COPY --from=ksops /go/bin/kustomize /usr/local/bin/kustomize

# Copy the plugin to kustomize plugin path
COPY --from=ksops /go/src/github.com/viaduct-ai/kustomize-sops/*  $KUSTOMIZE_PLUGIN_PATH/viaduct.ai/v1/ksops/

RUN chmod -R 775 /usr/local/bin /home/<USER>
RUN usermod -aG root argocd

USER argocd

RUN /usr/local/bin/helm.bin plugin install https://github.com/jkroepke/helm-secrets --version 3.8.1

ENV HELM_PLUGINS="/home/<USER>/.local/share/helm/plugins/"
