data "aws_ecr_authorization_token" "token" {}

data "aws_iam_policy_document" "ecr_read_access_policy_document" {
  statement {
    resources = [
      aws_ecr_repository.argocd.arn
    ]

    actions = [
      "ecr:BatchCheckLayerAvailability",
      "ecr:BatchGetImage",
      "ecr:GetDownloadUrlForLayer"
    ]
  }

  statement {
    resources = ["*"]

    actions = [
      "ecr:GetAuthorizationToken"
    ]
  }
}

resource "aws_ecr_repository" "argocd" {
  name                 = "argocd"
  image_tag_mutability = var.image_tag_mutability

  image_scanning_configuration {
    scan_on_push = true
  }
}

resource "docker_registry_image" "argocd" {
  name          = docker_image.argocd.name
  keep_remotely = true
}

resource "docker_image" "argocd" {
  name = "${aws_ecr_repository.argocd.repository_url}:${var.tag}"

  build {
    context = "${path.module}/docker"
  }
}

resource "aws_iam_policy" "ecr_read_access_policy" {
  description = "Allow read access to ECR"
  policy      = data.aws_iam_policy_document.ecr_read_access_policy_document.json
}

resource "aws_iam_role_policy_attachment" "ecr_read_access_policy_attachment" {
  count      = var.role_name != "" ? 1 : 0
  role       = var.role_name
  policy_arn = aws_iam_policy.ecr_read_access_policy.arn
}