variable "region" {
  description = "The AWS region to deploy resources"
  type        = string
  default     = "us-east-1"
}

variable "tag" {
  description = "The docker image tag"
  type        = string
  default     = "latest"
}

variable "role_name" {
  description = "The IAM role name to be granted Amazon ECR read access (i.e. '00000-dev-cluster00000000000000000000000000')"
  type        = string
  default     = ""
}

variable "image_tag_mutability" {
  description = "The tag mutability setting for the repository"
  type        = string
  default     = "IMMUTABLE"
}