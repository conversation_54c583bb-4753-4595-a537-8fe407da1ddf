{{- if .Values.dex.enabled }}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ template "argo-cd.dex.fullname" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.dex.name "name" .Values.dex.name) | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ template "argo-cd.dex.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ template "argo-cd.dexServiceAccountName" . }}
  namespace: {{ .Release.Namespace }}
{{- end }}