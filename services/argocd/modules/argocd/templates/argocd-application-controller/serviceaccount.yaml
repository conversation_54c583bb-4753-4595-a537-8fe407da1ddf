{{- if .Values.controller.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.controller.serviceAccount.automountServiceAccountToken }}
metadata:
  name: {{ template "argo-cd.controllerServiceAccountName" . }}
{{- if .Values.controller.serviceAccount.annotations }}
  annotations:
  {{- range $key, $value := .Values.controller.serviceAccount.annotations }}
    {{ $key }}: {{ $value | quote }}
  {{- end }}
{{- end }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.controller.name "name" .Values.controller.name) | nindent 4 }}
  {{- range $key, $value := .Values.controller.serviceAccount.labels }}
    {{ $key }}: {{ $value | quote }}
  {{- end }}
{{- end }}
