apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ template "argo-cd.controller.fullname" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.controller.name "name" .Values.controller.name) | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ template "argo-cd.controller.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ template "argo-cd.controllerServiceAccountName" . }}
  namespace: {{ .Release.Namespace }}