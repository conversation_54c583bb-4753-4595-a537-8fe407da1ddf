{{- if .Values.controller.args.statusProcessors }}
DEPRECATED option controller.args.statusProcessors - Use configs.params.controller.status.processors
{{- end }}
{{- if .Values.controller.args.operationProcessors }}
DEPRECATED option controller.args.operationProcessors - Use configs.params.controller.operation.processors
{{- end }}
{{- if .Values.controller.args.appResyncPeriod }}
DEPRECATED option controller.args.appResyncPeriod - Use server.config.timeout.reconciliation
{{- end }}
{{- if .Values.controller.args.appHardResyncPeriod }}
DEPRECATED option controller.args.appHardResyncPeriod - Use server.config.timeout.hard.reconciliation
{{- end }}
{{- if .Values.controller.args.selfHealTimeout }}
DEPRECATED option controller.args.selfHealTimeout -  Use configs.params.controller.self.heal.timeout.seconds
{{- end }}
{{- if .Values.controller.args.repoServerTimeoutSeconds }}
DEPRECATED option controller.args.repoServerTimeoutSeconds - Use configs.params.controller.repo.server.timeout.seconds
{{- end }}
{{- if .Values.controller.logFormat }}
DEPRECATED option controller.logFormat - Use configs.params.controller.log.format
{{- end }}
{{- if .Values.controller.logLevel }}
DEPRECATED option controller.logLevel - Use configs.params.controller.log.level
{{- end }}
{{- if .Values.server.logFormat }}
DEPRECATED option server.logFormat - Use configs.params.server.log.format
{{- end }}
{{- if .Values.server.logLevel }}
DEPRECATED option server.logLevel - Use configs.params.server.log.level
{{- end }}
{{- if has "--insecure" .Values.server.extraArgs }}
DEPRECATED option server.extraArgs."--insecure" - Use configs.params.server.insecure
{{- end }}
{{- if .Values.repoServer.logFormat }}
DEPRECATED option repoServer.logFormat - Use configs.params.repoServer.log.format
{{- end }}
{{- if .Values.repoServer.logLevel }}
DEPRECATED option repoServer.logLevel - Use configs.params.repoServer.log.level
{{- end }}
{{- if or .Values.server.config (hasKey .Values.server "configEnabled") .Values.server.configAnnotations }}
DEPRECATED option server.config - Use configs.cm
{{- end }}
{{- if or .Values.server.rbacConfig (hasKey .Values.server "rbacConfigCreate") .Values.server.rbacConfigAnnotations }}
DEPRECATED option server.rbacConfig - Use configs.rbac
{{- end }}
{{- if .Values.configs.secret.argocdServerTlsConfig }}
DEPRECATED option config.secret.argocdServerTlsConfig - Use server.certificate or server.certificateSecret
{{- end }}
{{- if .Values.configs.gpgKeys }}
DEPRECATED option configs.gpgKeys - Use config.gpg.keys
{{- end }}
{{- if .Values.configs.gpgKeysAnnotations }}
DEPRECATED option configs.gpgKeysAnnotations - Use config.gpg.annotations
{{- end }}
{{- if hasKey (.Values.controller.clusterAdminAccess | default dict) "enabled" }}
DEPRECATED option .controller.clusterAdminAccess.enabled - Use createClusterRoles
{{- end }}
{{- if hasKey (.Values.server.clusterAdminAccess | default dict) "enabled" }}
DEPRECATED option .server.clusterAdminAccess.enabled - Use createClusterRoles
{{- end }}
{{- if hasKey (.Values.repoServer.clusterAdminAccess | default dict) "enabled" }}
DEPRECATED option .server.clusterAdminAccess.enabled - Use createClusterRoles
{{- end }}
{{- if .Values.configs.knownHostsAnnotations }}
DEPRECATED option configs.knownHostsAnnotations - Use configs.ssh.annotations
{{- end }}
{{- if hasKey .Values.configs "knownHosts" }}
DEPRECATED option configs.knownHosts.data.ssh_known_hosts - Use configs.ssh.knownHosts
{{- end }}
{{- if .Values.configs.tlsCertsAnnotations }}
DEPRECATED option configs.tlsCertsAnnotations - Use configs.tls.annotations
{{- end }}
{{- if hasKey .Values.configs "tlsCerts" }}
DEPRECATED option configs.tlsCerts.data - Use configs.tls.certificates
{{- end }}
{{- if .Values.applicationSet.logFormat }}
DEPRECATED option applicationSet.logFormat - Use configs.params.applicationsetcontroller.log.format
{{- end }}
{{- if .Values.applicationSet.logLevel }}
DEPRECATED option applicationSet.logLevel - Use configs.params.applicationsetcontroller.log.level
{{- end }}
{{- if .Values.applicationSet.args.policy }}
DEPRECATED option applicationSet.args.policy - Use configs.params.applicationsetcontroller.policy
{{- end }}
{{- if .Values.applicationSet.args.dryRun }}
DEPRECATED option applicationSet.args.dryRun - Use configs.params.applicationsetcontroller.dryRun
{{- end }}
{{- if .Values.controller.service }}
REMOVED option controller.service - Use controller.metrics
{{- end }}
{{- if .Values.repoServer.copyutil }}
REMOVED option repoSever.copyutil.resources - Use repoServer.resources
{{- end }}
{{- if .Values.applicationSet.args.debug }}
REMOVED option applicationSet.args.debug - Use applicationSet.logLevel: debug
{{- end }}
{{- if .Values.applicationSet.args.enableLeaderElection }}
REMOVED option applicationSet.args.enableLeaderElection - Value determined based on replicas
{{- end }}
{{- if .Values.controller.containerPort }}
REMOVED option controller.containerPort - Use controller.containerPorts
{{- end }}
{{- if .Values.server.containerPort }}
REMOVED option server.containerPort - Use server.containerPorts
{{- end }}
{{- if .Values.repoServer.containerPort }}
REMOVED option repoServer.containerPort - Use repoServer.containerPorts
{{- end }}
{{- if .Values.applicationSet.args.metricsAddr }}
REMOVED option applicationSet.args.metricsAddr - Use applicationSet.containerPorts
{{- end }}
{{- if .Values.applicationSet.args.probeBindAddr }}
REMOVED option applicationSet.args.probeBindAddr - Use applicationSet.containerPorts
{{- end }}
{{- if .Values.redis.containerPort }}
REMOVED option redis.containerPort - Use redis.containerPorts
{{- end }}
{{- if .Values.redis.metrics.containerPort }}
REMOVED option redis.metrics.containerPort - Use redis.containerPorts
{{- end }}

In order to access the server UI you have the following options:

1. kubectl port-forward service/{{ include "argo-cd.fullname" . }}-server -n {{ .Release.Namespace }} 8080:443

    and then open the browser on http://localhost:8080 and accept the certificate

2. enable ingress in the values file `server.ingress.enabled` and either
      - Add the annotation for ssl passthrough: https://argo-cd.readthedocs.io/en/stable/operator-manual/ingress/#option-1-ssl-passthrough
      - Set the `configs.params."server.insecure"` in the values file and terminate SSL at your ingress: https://argo-cd.readthedocs.io/en/stable/operator-manual/ingress/#option-2-multiple-ingress-objects-and-hosts


{{ if eq (toString (index (coalesce .Values.server.config .Values.configs.cm) "admin.enabled")) "true" -}}
After reaching the UI the first time you can login with username: admin and the random password generated during the installation. You can find the password by running:

kubectl -n {{ .Release.Namespace }} get secret argocd-initial-admin-secret -o jsonpath="{.data.password}" | base64 -d

(You should delete the initial secret afterwards as suggested by the Getting Started Guide: https://argo-cd.readthedocs.io/en/stable/getting_started/#4-login-using-the-cli)
{{ else if or (index .Values.configs.cm "dex.config") (index .Values.configs.cm "oidc.config") -}}
After reaching the UI the first time you can login using Dex or OIDC.
{{ else -}}
After reaching the UI the first time you cannot login with username and password since you've disabled it. You should enable admin back or configure Dex via `configs.cm.dex.config` or OIDC via `configs.cm.oidc.config`.
{{ end -}}
