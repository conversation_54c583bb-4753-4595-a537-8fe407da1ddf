{{- if and .Values.notifications.enabled .Values.notifications.cm.create }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-notifications-cm
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.notifications.name "name" .Values.notifications.name) | nindent 4 }}
data:
  context: |
    argocdUrl: {{ .Values.notifications.argocdUrl | quote }}
    {{- with .Values.notifications.context }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.notifications.notifiers }}
    {{- toYaml . | nindent 2 }}
  {{- end }}
  {{- with .Values.notifications.subscriptions }}
  subscriptions: |
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with .Values.notifications.templates }}
    {{- toYaml . | nindent 2 }}
  {{- end }}
  {{- with .Values.notifications.triggers }}
    {{- toYaml . | nindent 2 }}
  {{- end }}
{{- end }}
