{{- if .Values.repoServer.certificateSecret.enabled }}
apiVersion: v1
kind: Secret
metadata:
  name: argocd-repo-server-tls
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.repoServer.name "name" "repo-server-tls") | nindent 4 }}
    {{- with .Values.repoServer.certificateSecret.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.repoServer.certificateSecret.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
type: kubernetes.io/tls
data:
  {{- with .Values.repoServer.certificateSecret.ca }}
  ca.crt: {{ . | b64enc | quote }}
  {{- end }}
  tls.crt: {{ .Values.repoServer.certificateSecret.crt | b64enc | quote }}
  tls.key: {{ .Values.repoServer.certificateSecret.key | b64enc | quote }}
{{- end }}
