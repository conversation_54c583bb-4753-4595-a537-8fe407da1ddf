{{- if and .Values.notifications.enabled .Values.notifications.secret.create }}
apiVersion: v1
kind: Secret
metadata:
  name: argocd-notifications-secret
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.notifications.name "name" .Values.notifications.name) | nindent 4 }}
  {{- with .Values.notifications.secret.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
type: Opaque
stringData:
  {{- with .Values.notifications.secret.items }}
    {{- toYaml . | nindent 2 }}
  {{- end }}
{{- end }}
