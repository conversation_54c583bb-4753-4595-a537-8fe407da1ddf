{{- if (hasKey .Values.server "configEnabled") | ternary .Values.server.configEnabled .Values.configs.cm.create }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-cm
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" "cm") | nindent 4 }}
  {{- with (mergeOverwrite (deepCopy .Values.configs.cm.annotations) (.Values.server.configAnnotations | default dict)) }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
data:
  {{- include "argo-cd.config.cm" . | trim | nindent 2 }}
{{- end }}
