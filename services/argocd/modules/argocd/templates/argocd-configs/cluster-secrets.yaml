{{- range .Values.configs.clusterCredentials }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "argo-cd.name" $ }}-cluster-{{ .name }}
  labels:
    {{- include "argo-cd.labels" (dict "context" $) | nindent 4 }}
    {{- with .labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
    argocd.argoproj.io/secret-type: cluster
  {{- with .annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
type: Opaque
stringData:
  name: {{ required "A valid .Values.configs.clusterCredentials[].name entry is required!" .name }}
  server: {{ required "A valid .Values.configs.clusterCredentials[].server entry is required!" .server }}
  {{- if .namespaces }}
  namespaces: {{ .namespaces }}
    {{- if .clusterResources }}
  clusterResources: {{ .clusterResources | quote }}
    {{- end }}
  {{- end }}
  config: |
    {{- required "A valid .Values.configs.clusterCredentials[].config entry is required!" .config | toRawJson | nindent 4 }}
{{- end }}
