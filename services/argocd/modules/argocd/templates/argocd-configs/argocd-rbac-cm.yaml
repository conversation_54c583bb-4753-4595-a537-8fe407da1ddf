{{- if (hasKey .Values.server "rbacConfigCreate") | ternary .Values.server.rbacConfigCreate .Values.configs.rbac.create }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-rbac-cm
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" "rbac-cm") | nindent 4 }}
  {{- with (mergeOverwrite (deepCopy .Values.configs.rbac.annotations) (.Values.server.rbacConfigAnnotations | default dict)) }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
{{- with (mergeOverwrite (deepCopy (omit .Values.configs.rbac "create" "annotations")) (.Values.server.rbacConfig | default dict)) }}
data:
  {{- toYaml . | nindent 2 }}
{{- end }}
{{- end }}
