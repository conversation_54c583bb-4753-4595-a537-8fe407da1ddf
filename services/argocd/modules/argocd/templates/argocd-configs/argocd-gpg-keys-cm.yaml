apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-gpg-keys-cm
  labels:
    {{- include "argo-cd.labels" (dict "context" . "name" "gpg-keys-cm") | nindent 4 }}
  {{ with (mergeOverwrite (deepCopy .Values.configs.gpg.annotations) (.Values.configs.gpgKeysAnnotations | default dict)) -}}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
{{ with (mergeOverwrite (deepCopy .Values.configs.gpg.keys) (.Values.configs.gpgKeys | default dict)) -}}
data:
  {{- toYaml . | nindent 2 }}
{{- end }}
