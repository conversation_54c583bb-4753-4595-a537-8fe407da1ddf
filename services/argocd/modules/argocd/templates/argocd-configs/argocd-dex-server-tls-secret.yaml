{{- if and .Values.dex.enabled .Values.dex.certificateSecret.enabled }}
apiVersion: v1
kind: Secret
metadata:
  name: argocd-dex-server-tls
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.dex.name "name" "dex-server-tls") | nindent 4 }}
    {{- with .Values.dex.certificateSecret.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.dex.certificateSecret.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
type: kubernetes.io/tls
data:
  {{- with .Values.dex.certificateSecret.ca }}
  ca.crt: {{ . | b64enc | quote }}
  {{- end }}
  tls.crt: {{ .Values.dex.certificateSecret.crt | b64enc | quote }}
  tls.key: {{ .Values.dex.certificateSecret.key | b64enc | quote }}
{{- end }}
