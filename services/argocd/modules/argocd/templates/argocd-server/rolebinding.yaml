apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ template "argo-cd.server.fullname" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ template "argo-cd.server.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ template "argo-cd.serverServiceAccountName" . }}
  namespace: {{ .Release.Namespace }}