apiVersion: apps/v1
kind: Deployment
metadata:
  {{- with (mergeOverwrite (deepCopy .Values.global.deploymentAnnotations) .Values.server.deploymentAnnotations) }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
  name: {{ template "argo-cd.server.fullname" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 4 }}
spec:
  {{- if not .Values.server.autoscaling.enabled }}
  replicas: {{ .Values.server.replicas }}
  {{- end }}
  revisionHistoryLimit: {{ .Values.global.revisionHistoryLimit }}
  selector:
    matchLabels:
      {{- include "argo-cd.selectorLabels" (dict "context" . "name" .Values.server.name) | nindent 6 }}
  template:
    metadata:
      annotations:
        checksum/cmd-params: {{ include (print $.Template.BasePath "/argocd-configs/argocd-cmd-params-cm.yaml") . | sha256sum }}
        {{- with (mergeOverwrite (deepCopy .Values.global.podAnnotations) .Values.server.podAnnotations) }}
        {{- range $key, $value := . }}
        {{ $key }}: {{ $value | quote }}
        {{- end }}
        {{- end }}
      labels:
        {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 8 }}
        {{- with (mergeOverwrite (deepCopy .Values.global.podLabels) .Values.server.podLabels) }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.server.imagePullSecrets | default .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.global.hostAliases }}
      hostAliases:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.global.securityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.server.priorityClassName | default .Values.global.priorityClassName }}
      priorityClassName: {{ . }}
      {{- end }}
      serviceAccountName: {{ include "argo-cd.serverServiceAccountName" . }}
      containers:
      - name: {{ .Values.server.name }}
        image: {{ default .Values.global.image.repository .Values.server.image.repository }}:{{ default (include "argo-cd.defaultTag" .) .Values.server.image.tag }}
        imagePullPolicy: {{ default .Values.global.image.imagePullPolicy .Values.server.image.imagePullPolicy }}
        command:
        - argocd-server
        - --port={{ .Values.server.containerPorts.server }}
        - --metrics-port={{ .Values.server.containerPorts.metrics }}
        {{- with .Values.server.logFormat }}
        - --logformat
        - {{ . | quote }}
        {{- end }}
        {{- with .Values.server.logLevel }}
        - --loglevel
        - {{ . | quote }}
        {{- end }}
        {{- with .Values.server.extraArgs }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        env:
          {{- with .Values.server.env }}
            {{- toYaml . | nindent 10 }}
          {{- end }}
          - name: ARGOCD_SERVER_INSECURE
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.insecure
                optional: true
          - name: ARGOCD_SERVER_BASEHREF
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.basehref
                optional: true
          - name: ARGOCD_SERVER_ROOTPATH
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.rootpath
                optional: true
          - name: ARGOCD_SERVER_LOGFORMAT
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.log.format
                optional: true
          - name: ARGOCD_SERVER_LOG_LEVEL
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.log.level
                optional: true
          - name: ARGOCD_SERVER_REPO_SERVER
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: repo.server
                optional: true
          - name: ARGOCD_SERVER_DEX_SERVER
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.dex.server
                optional: true
          - name: ARGOCD_SERVER_DISABLE_AUTH
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.disable.auth
                optional: true
          - name: ARGOCD_SERVER_ENABLE_GZIP
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.enable.gzip
                optional: true
          - name: ARGOCD_SERVER_REPO_SERVER_TIMEOUT_SECONDS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.repo.server.timeout.seconds
                optional: true
          - name: ARGOCD_SERVER_X_FRAME_OPTIONS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.x.frame.options
                optional: true
          - name: ARGOCD_SERVER_CONTENT_SECURITY_POLICY
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.content.security.policy
                optional: true
          - name: ARGOCD_SERVER_REPO_SERVER_PLAINTEXT
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.repo.server.plaintext
                optional: true
          - name: ARGOCD_SERVER_REPO_SERVER_STRICT_TLS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.repo.server.strict.tls
                optional: true
          - name: ARGOCD_SERVER_DEX_SERVER_PLAINTEXT
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.dex.server.plaintext
                optional: true
          - name: ARGOCD_SERVER_DEX_SERVER_STRICT_TLS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.dex.server.strict.tls
                optional: true
          - name: ARGOCD_TLS_MIN_VERSION
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.tls.minversion
                optional: true
          - name: ARGOCD_TLS_MAX_VERSION
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.tls.maxversion
                optional: true
          - name: ARGOCD_TLS_CIPHERS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.tls.ciphers
                optional: true
          - name: ARGOCD_SERVER_CONNECTION_STATUS_CACHE_EXPIRATION
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.connection.status.cache.expiration
                optional: true
          - name: ARGOCD_SERVER_OIDC_CACHE_EXPIRATION
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.oidc.cache.expiration
                optional: true
          - name: ARGOCD_SERVER_LOGIN_ATTEMPTS_EXPIRATION
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.login.attempts.expiration
                optional: true
          - name: ARGOCD_SERVER_STATIC_ASSETS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.staticassets
                optional: true
          - name: ARGOCD_APP_STATE_CACHE_EXPIRATION
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.app.state.cache.expiration
                optional: true
          - name: REDIS_SERVER
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: redis.server
                optional: true
          - name: REDIS_COMPRESSION
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: redis.compression
                optional: true
          - name: REDISDB
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: redis.db
                optional: true
          - name: REDIS_USERNAME
            valueFrom:
              secretKeyRef:
                name: {{ default (include "argo-cd.redis.fullname" .) .Values.externalRedis.existingSecret }}
                key: redis-username
                optional: true
          - name: REDIS_PASSWORD
            valueFrom:
              secretKeyRef:
                name: {{ default (include "argo-cd.redis.fullname" .) .Values.externalRedis.existingSecret }}
                key: redis-password
                optional: true
          - name: ARGOCD_DEFAULT_CACHE_EXPIRATION
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.default.cache.expiration
                optional: true
          - name: ARGOCD_MAX_COOKIE_NUMBER
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.http.cookie.maxnumber
                optional: true
          - name: ARGOCD_SERVER_OTLP_ADDRESS
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: otlp.address
                optional: true
          - name: ARGOCD_APPLICATION_NAMESPACES
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: application.namespaces
                optional: true
          - name: ARGOCD_SERVER_ENABLE_PROXY_EXTENSION
            valueFrom:
              configMapKeyRef:
                name: argocd-cmd-params-cm
                key: server.enable.proxy.extension
                optional: true
        {{- with .Values.server.envFrom }}
        envFrom:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        volumeMounts:
        {{- with .Values.server.volumeMounts }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        - mountPath: /app/config/ssh
          name: ssh-known-hosts
        - mountPath: /app/config/tls
          name: tls-certs
        - mountPath: /app/config/server/tls
          name: argocd-repo-server-tls
        - mountPath: /app/config/dex/tls
          name: argocd-dex-server-tls
        - mountPath: /home/<USER>
          name: plugins-home
        - mountPath: /shared/app/custom
          name: styles
        - mountPath: /tmp
          name: tmp
        {{- if .Values.server.extensions.enabled }}
        - mountPath: /tmp/extensions
          name: extensions
        {{- end }}
        ports:
        - name: server
          containerPort: {{ .Values.server.containerPorts.server }}
          protocol: TCP
        - name: metrics
          containerPort: {{ .Values.server.containerPorts.metrics }}
          protocol: TCP
        livenessProbe:
          httpGet:
            path: /healthz?full=true
            port: server
          initialDelaySeconds: {{ .Values.server.livenessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.server.livenessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.server.livenessProbe.timeoutSeconds }}
          successThreshold: {{ .Values.server.livenessProbe.successThreshold }}
          failureThreshold: {{ .Values.server.livenessProbe.failureThreshold }}
        readinessProbe:
          httpGet:
            path: /healthz
            port: server
          initialDelaySeconds: {{ .Values.server.readinessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.server.readinessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.server.readinessProbe.timeoutSeconds }}
          successThreshold: {{ .Values.server.readinessProbe.successThreshold }}
          failureThreshold: {{ .Values.server.readinessProbe.failureThreshold }}
        resources:
          {{- toYaml .Values.server.resources | nindent 10 }}
        securityContext:
          {{- toYaml .Values.server.containerSecurityContext | nindent 10 }}
        {{- with .Values.server.lifecycle }}
        lifecycle:
          {{- toYaml . | nindent 10 }}
        {{- end }}
      {{- if .Values.server.extensions.enabled }}
      - name: argocd-extensions
        image: {{ .Values.server.extensions.image.repository }}:{{ .Values.server.extensions.image.tag }}
        imagePullPolicy: {{ .Values.server.extensions.image.imagePullPolicy }}
        resources:
          {{- toYaml .Values.server.extensions.resources | nindent 10 }}
        securityContext:
          {{- toYaml .Values.server.extensions.containerSecurityContext | nindent 10 }}
        volumeMounts:
          - name: extensions
            mountPath: /tmp/extensions/
          - name: tmp
            mountPath: /tmp
      {{- end }}
      {{- with .Values.server.extraContainers }}
        {{- tpl (toYaml .) $ | nindent 6 }}
      {{- end }}
      {{- with .Values.server.initContainers }}
      initContainers:
        {{- tpl (toYaml .) $ | nindent 6 }}
      {{- end }}
      {{- with include "argo-cd.affinity" (dict "context" . "component" .Values.server) }}
      affinity:
        {{- trim . | nindent 8 }}
      {{- end }}
      {{- with .Values.server.nodeSelector | default .Values.global.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.server.tolerations | default .Values.global.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.server.topologySpreadConstraints | default .Values.global.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- range $constraint := . }}
      - {{ toYaml $constraint | nindent 8 | trim }}
        {{- if not $constraint.labelSelector }}
        labelSelector:
          matchLabels:
            {{- include "argo-cd.selectorLabels" (dict "context" $ "name" $.Values.server.name) | nindent 12 }}
        {{- end }}
        {{- end }}
      {{- end }}
      volumes:
      {{- with .Values.server.volumes }}
        {{- toYaml . | nindent 6}}
      {{- end }}
      {{- if .Values.server.extensions.enabled }}
      - name: extensions
        emptyDir: {}
      {{- end }}
      - name: plugins-home
        emptyDir: {}
      - name: tmp
        emptyDir: {}
      - name: ssh-known-hosts
        configMap:
          name: argocd-ssh-known-hosts-cm
      - name: tls-certs
        configMap:
          name: argocd-tls-certs-cm
      - name: styles
        configMap:
          name: argocd-styles-cm
          optional: true
      - name: argocd-repo-server-tls
        secret:
          secretName: argocd-repo-server-tls
          optional: true
          items:
          - key: tls.crt
            path: tls.crt
          - key: tls.key
            path: tls.key
          - key: ca.crt
            path: ca.crt
      - name: argocd-dex-server-tls
        secret:
          secretName: argocd-dex-server-tls
          optional: true
          items:
          - key: tls.crt
            path: tls.crt
          - key: ca.crt
            path: ca.crt
      hostNetwork: {{ .Values.server.hostNetwork }}
      {{- with .Values.server.dnsConfig }}
      dnsConfig:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      dnsPolicy: {{ .Values.server.dnsPolicy }}
