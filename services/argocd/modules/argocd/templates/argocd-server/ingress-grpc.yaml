{{- if and .Values.server.ingressGrpc.enabled (not .Values.server.ingressGrpc.isAWSALB) -}}
{{- $servicePort := ternary .Values.server.service.servicePortHttps .Values.server.service.servicePortHttp .Values.server.ingressGrpc.https -}}
{{- $paths := .Values.server.ingressGrpc.paths -}}
{{- $extraPaths := .Values.server.ingressGrpc.extraPaths -}}
{{- $pathType := .Values.server.ingressGrpc.pathType -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "argo-cd.server.fullname" . }}-grpc
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 4 }}
    {{- with .Values.server.ingressGrpc.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.server.ingressGrpc.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
spec:
  {{- with .Values.server.ingressGrpc.ingressClassName }}
  ingressClassName: {{ . }}
  {{- end }}
  rules:
  {{- if .Values.server.ingressGrpc.hosts }}
    {{- range $host := .Values.server.ingressGrpc.hosts }}
    - host: {{ $host }}
      http:
        paths:
          {{- with $extraPaths }}
          {{- toYaml . | nindent 10 }}
          {{- end }}
          {{- range $p := $paths }}
          - path: {{ $p }}
            pathType: {{ $pathType }}
            backend:
              service:
                name: {{ include "argo-cd.server.fullname" $ }}
                port:
                  {{- if kindIs "float64" $servicePort }}
                  number: {{ $servicePort }}
                  {{- else }}
                  name: {{ $servicePort }}
                  {{- end }}
          {{- end -}}
    {{- end -}}
  {{- else }}
    - http:
        paths:
          {{- with $extraPaths }}
          {{- toYaml . | nindent 10 }}
          {{- end }}
          {{- range $p := $paths }}
          - path: {{ $p }}
            pathType: {{ $pathType }}
            backend:
              service:
                name: {{ include "argo-cd.server.fullname" $ }}
                port:
                  {{- if kindIs "float64" $servicePort }}
                  number: {{ $servicePort }}
                  {{- else }}
                  name: {{ $servicePort }}
                  {{- end }}
          {{- end -}}
  {{- end -}}
  {{- with .Values.server.ingressGrpc.tls }}
  tls:
    {{- toYaml . | nindent 4 }}
  {{- end -}}
{{- end -}}
