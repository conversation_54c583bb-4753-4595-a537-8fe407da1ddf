{{- if and .Values.server.metrics.enabled .Values.server.metrics.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "argo-cd.server.fullname" . }}
  {{- if .Values.server.metrics.serviceMonitor.namespace }}
  namespace: {{ .Values.server.metrics.serviceMonitor.namespace }}
  {{- end }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 4 }}
    {{- with .Values.server.metrics.serviceMonitor.selector }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
    {{- with .Values.server.metrics.serviceMonitor.additionalLabels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  annotations:
    {{- range $key, $value := .Values.server.metrics.serviceMonitor.annotations }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
spec:
  endpoints:
    - port: {{ .Values.server.metrics.service.portName }}
      {{- with .Values.server.metrics.serviceMonitor.interval }}
      interval: {{ . }}
      {{- end }}
      path: /metrics
      {{- with .Values.server.metrics.serviceMonitor.relabelings }}
      relabelings:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.server.metrics.serviceMonitor.metricRelabelings }}
      metricRelabelings:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.server.metrics.serviceMonitor.scheme }}
      scheme: {{ . }}
      {{- end }}
      {{- with .Values.server.metrics.serviceMonitor.tlsConfig }}
      tlsConfig:
        {{- toYaml . | nindent 8 }}
      {{- end }}
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace }}
  selector:
    matchLabels:
      {{- include "argo-cd.selectorLabels" (dict "context" . "component" .Values.server.name "name" (printf "%s-metrics" .Values.server.name)) | nindent 6 }}
{{- end }}
