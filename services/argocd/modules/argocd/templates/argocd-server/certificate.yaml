{{- if .Values.server.certificate.enabled -}}
apiVersion: {{ include "argo-cd.apiVersion.cert-manager" . }}
kind: Certificate
metadata:
  name: {{ include "argo-cd.server.fullname" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 4 }}
spec:
  secretName: {{ .Values.server.certificate.secretName }}
  commonName: {{ .Values.server.certificate.domain | quote }}
  dnsNames:
    - {{ .Values.server.certificate.domain | quote }}
    {{- range .Values.server.certificate.additionalHosts }}
    - {{ . | quote }}
    {{- end }}
  {{- with .Values.server.certificate.duration }}
  duration: {{ . | quote }}
  {{- end }}
  {{- with .Values.server.certificate.renewBefore }}
  renewBefore: {{ . | quote }}
  {{- end }}
  issuerRef:
    {{- with .Values.server.certificate.issuer.group }}
    group: {{ . | quote }}
    {{- end }}
    kind: {{ .Values.server.certificate.issuer.kind | quote }}
    name: {{ .Values.server.certificate.issuer.name | quote }}
  {{- with .Values.server.certificate.privateKey }}
  privateKey:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
