{{- if .Values.server.metrics.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "argo-cd.server.fullname" . }}-metrics
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" (printf "%s-metrics" .Values.server.name)) | nindent 4 }}
    {{- with .Values.server.metrics.service.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.server.metrics.service.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
spec:
  ports:
  - name: {{ .Values.server.metrics.service.portName }}
    protocol: TCP
    port: {{ .Values.server.metrics.service.servicePort }}
    targetPort: metrics
  selector:
    {{- include "argo-cd.selectorLabels" (dict "context" . "name" .Values.server.name) | nindent 4 }}
{{- end }}
