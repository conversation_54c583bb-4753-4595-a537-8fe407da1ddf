{{- $config := .Values.server.clusterAdminAccess | default dict -}}
{{- if hasKey $config "enabled" | ternary $config.enabled .Values.createClusterRoles }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "argo-cd.server.fullname" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 4 }}
rules:
  - apiGroups:
      - '*'
    resources:
      - '*'
    verbs:
      - delete
      - get
      - patch
  - apiGroups:
      - ""
    resources:
      - events
    verbs:
      - list
  - apiGroups:
      - ""
    resources:
      - pods
      - pods/log
    verbs:
      - get
  {{- if eq (toString (index (coalesce .Values.server.config .Values.configs.cm) "exec.enabled")) "true" }}
  - apiGroups:
      - ""
    resources:
      - pods/exec
    verbs:
      - create
  {{- end }}
  - apiGroups:
      - argoproj.io
    resources:
      - applications
    verbs:
      - get
      - list
      - update
      - watch
{{- end }}
