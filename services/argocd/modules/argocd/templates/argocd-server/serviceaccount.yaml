{{- if .Values.server.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.server.serviceAccount.automountServiceAccountToken }}
metadata:
  name: {{ template "argo-cd.serverServiceAccountName" . }}
{{- if .Values.server.serviceAccount.annotations }}
  annotations:
  {{- range $key, $value := .Values.server.serviceAccount.annotations }}
    {{ $key }}: {{ $value | quote }}
  {{- end }}
{{- end }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.server.name "name" .Values.server.name) | nindent 4 }}
  {{- range $key, $value := .Values.server.serviceAccount.labels }}
    {{ $key }}: {{ $value | quote }}
  {{- end }}
{{- end }}
