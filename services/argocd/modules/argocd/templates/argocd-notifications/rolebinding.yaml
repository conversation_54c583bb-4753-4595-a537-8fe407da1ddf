{{- if .Values.notifications.enabled }}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ template "argo-cd.notifications.fullname" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.notifications.name "name" .Values.notifications.name) | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ template "argo-cd.notifications.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ template "argo-cd.notificationsServiceAccountName" . }}
    namespace: {{ .Release.Namespace }}
{{- end }}
