{{- if and .Values.notifications.enabled .Values.notifications.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.notifications.serviceAccount.automountServiceAccountToken }}
metadata:
  name: {{ template "argo-cd.notificationsServiceAccountName" . }}
{{- if .Values.notifications.serviceAccount.annotations }}
  annotations:
  {{- range $key, $value := .Values.notifications.serviceAccount.annotations }}
    {{ $key }}: {{ $value | quote }}
  {{- end }}
{{- end }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.notifications.name "name" .Values.notifications.name) | nindent 4 }}
  {{- range $key, $value := .Values.notifications.serviceAccount.labels }}
    {{ $key }}: {{ $value | quote }}
  {{- end }}
{{- end }}
