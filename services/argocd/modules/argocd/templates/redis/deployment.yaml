{{- $redisHa := index .Values "redis-ha" -}}
{{- if and .Values.redis.enabled (not $redisHa.enabled) -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  {{- with (mergeOverwrite (deepCopy .Values.global.deploymentAnnotations) .Values.redis.deploymentAnnotations) }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
  name: {{ include "argo-cd.redis.fullname" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.redis.name "name" .Values.redis.name) | nindent 4 }}
spec:
  replicas: 1
  revisionHistoryLimit: {{ .Values.global.revisionHistoryLimit }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "argo-cd.name" . }}-{{ .Values.redis.name }}
  template:
    metadata:
      labels:
        {{- include "argo-cd.labels" (dict "context" . "component" .Values.redis.name "name" .Values.redis.name) | nindent 8 }}
        {{- with (mergeOverwrite (deepCopy .Values.global.podLabels) .Values.redis.podLabels) }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with (mergeOverwrite (deepCopy .Values.global.podAnnotations) .Values.redis.podAnnotations) }}
      annotations:
        {{- range $key, $value := . }}
        {{ $key }}: {{ $value | quote }}
        {{- end }}
      {{- end }}
    spec:
      {{- with .Values.redis.imagePullSecrets | default .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.global.hostAliases }}
      hostAliases:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.redis.securityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.redis.priorityClassName | default .Values.global.priorityClassName }}
      priorityClassName: {{ . }}
      {{- end }}
      serviceAccountName: {{ include "argo-cd.redisServiceAccountName" . }}
      containers:
      - name: {{ .Values.redis.name }}
        image: {{ .Values.redis.image.repository }}:{{ .Values.redis.image.tag }}
        imagePullPolicy: {{ default .Values.global.image.imagePullPolicy .Values.redis.image.imagePullPolicy }}
        args:
        - --save
        - ""
        - --appendonly
        - "no"
        {{- with .Values.redis.extraArgs }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- with .Values.redis.env }}
        env:
          {{- toYaml . | nindent 8 }}
        {{- end }}
        {{- with .Values.redis.envFrom }}
        envFrom:
          {{- toYaml . | nindent 8 }}
        {{- end }}
        ports:
        - name: redis
          containerPort: {{ .Values.redis.containerPorts.redis }}
          protocol: TCP
        resources:
          {{- toYaml .Values.redis.resources | nindent 10 }}
        securityContext:
          {{- toYaml .Values.redis.containerSecurityContext | nindent 10 }}
        {{- with .Values.redis.volumeMounts }}
        volumeMounts:
          {{- toYaml . | nindent 10 }}
        {{- end }}
      {{- if .Values.redis.exporter.enabled }}
      - name: metrics
        image: {{ .Values.redis.exporter.image.repository }}:{{ .Values.redis.exporter.image.tag }}
        imagePullPolicy: {{ default .Values.global.image.imagePullPolicy .Values.redis.exporter.image.imagePullPolicy }}
        env:
        - name: REDIS_ADDR
          value: {{ printf "redis://localhost:%v" .Values.redis.containerPorts.redis }}
        - name: REDIS_EXPORTER_WEB_LISTEN_ADDRESS
          value: {{ printf "0.0.0.0:%v" .Values.redis.containerPorts.metrics }}
        {{- with .Values.redis.exporter.env }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
        ports:
        - name: metrics
          containerPort: {{ .Values.redis.containerPorts.metrics }}
          protocol: TCP
        resources:
          {{- toYaml .Values.redis.exporter.resources | nindent 10 }}
        securityContext:
          {{- toYaml .Values.redis.exporter.containerSecurityContext | nindent 10 }}
      {{- end }}
      {{- with .Values.redis.extraContainers }}
        {{- tpl (toYaml .) $ | nindent 6 }}
      {{- end }}
      {{- with .Values.redis.initContainers }}
      initContainers:
        {{- tpl (toYaml .) $ | nindent 6 }}
      {{- end }}
      {{- with .Values.redis.nodeSelector | default .Values.global.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.redis.tolerations | default .Values.global.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with include "argo-cd.affinity" (dict "context" . "component" .Values.redis) }}
      affinity:
        {{-  trim . | nindent 8 }}
      {{- end }}
      {{- with .Values.redis.topologySpreadConstraints | default .Values.global.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- range $constraint := . }}
      - {{ toYaml $constraint | nindent 8 | trim }}
        {{- if not $constraint.labelSelector }}
        labelSelector:
          matchLabels:
            app.kubernetes.io/name: {{ include "argo-cd.name" $ }}-{{ $.Values.redis.name }}
        {{- end }}
        {{- end }}
      {{- end }}
      {{- with .Values.redis.volumes }}
      volumes:
        {{- toYaml . | nindent 8}}
      {{- end }}
      {{- with .Values.redis.dnsConfig }}
      dnsConfig:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      dnsPolicy: {{ .Values.redis.dnsPolicy }}
{{- end }}
