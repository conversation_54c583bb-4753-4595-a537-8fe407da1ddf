{{- $redisHa := (index .Values "redis-ha") -}}
{{- if and .Values.redis.enabled (not $redisHa.enabled) .Values.redis.metrics.enabled -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "argo-cd.redis.fullname" . }}-metrics
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.redis.name "name" .Values.redis.name) | nindent 4 }}
    {{- with .Values.redis.metrics.service.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.redis.metrics.service.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
spec:
  type: {{ .Values.redis.metrics.service.type }}
  {{- with .Values.redis.metrics.service.clusterIP }}
  clusterIP: {{ . }}
  {{- end }}
  ports:
    - name: {{ .Values.redis.metrics.service.portName }}
      protocol: TCP
      port: {{ .Values.redis.metrics.service.servicePort }}
      targetPort: metrics
  selector:
    {{- include "argo-cd.selectorLabels" (dict "context" . "component" .Values.redis.name "name" .Values.redis.name) | nindent 4 }}
{{- end }}
