{{- if and .Values.applicationSet.enabled .Values.applicationSet.webhook.ingress.enabled -}}
{{- $servicePort := .Values.applicationSet.service.portName -}}
{{- $paths := .Values.applicationSet.webhook.ingress.paths -}}
{{- $extraPaths := .Values.applicationSet.webhook.ingress.extraPaths -}}
{{- $pathType := .Values.applicationSet.webhook.ingress.pathType -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "argo-cd.applicationSet.fullname" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.applicationSet.name "name" .Values.applicationSet.name) | nindent 4 }}
    {{- with .Values.applicationSet.webhook.ingress.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.applicationSet.webhook.ingress.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
spec:
  {{- with .Values.applicationSet.webhook.ingress.ingressClassName }}
  ingressClassName: {{ . }}
  {{- end }}
  rules:
  {{- if .Values.applicationSet.webhook.ingress.hosts }}
    {{- range $host := .Values.applicationSet.webhook.ingress.hosts }}
    - host: {{ $host }}
      http:
        paths:
          {{- with $extraPaths }}
          {{- toYaml . | nindent 10 }}
          {{- end }}
          {{- range $p := $paths }}
          - path: {{ $p }}
            pathType: {{ $pathType }}
            backend:
              service:
                name: {{ include "argo-cd.applicationSet.fullname" $ }}
                port:
                  {{- if kindIs "float64" $servicePort }}
                  number: {{ $servicePort }}
                  {{- else }}
                  name: {{ $servicePort }}
                  {{- end }}
          {{- end -}}
    {{- end -}}
  {{- else }}
    - http:
        paths:
          {{- with $extraPaths }}
          {{- toYaml . | nindent 10 }}
          {{- end }}
          {{- range $p := $paths }}
          - path: {{ $p }}
            pathType: {{ $pathType }}
            backend:
              service:
                name: {{ include "argo-cd.applicationSet.fullname" $ }}
                port:
                  {{- if kindIs "float64" $servicePort }}
                  number: {{ $servicePort }}
                  {{- else }}
                  name: {{ $servicePort }}
                  {{- end }}
          {{- end -}}
  {{- end -}}
  {{- with .Values.applicationSet.webhook.ingress.tls }}
  tls:
    {{- toYaml . | nindent 4 }}
  {{- end -}}
{{- end -}}
