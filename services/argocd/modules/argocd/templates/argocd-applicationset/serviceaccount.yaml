{{- if and .Values.applicationSet.enabled .Values.applicationSet.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.applicationSet.serviceAccount.automountServiceAccountToken }}
metadata:
  name: {{ template "argo-cd.applicationSetServiceAccountName" . }}
{{- if .Values.applicationSet.serviceAccount.annotations }}
  annotations:
  {{- range $key, $value := .Values.applicationSet.serviceAccount.annotations }}
    {{ $key }}: {{ $value | quote }}
  {{- end }}
{{- end }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.applicationSet.name "name" .Values.applicationSet.name) | nindent 4 }}
  {{- range $key, $value := .Values.applicationSet.serviceAccount.labels }}
    {{ $key }}: {{ $value | quote }}
  {{- end }}
{{- end }}
