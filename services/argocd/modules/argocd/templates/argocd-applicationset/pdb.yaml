{{- if and .Values.applicationSet.enabled .Values.applicationSet.pdb.enabled }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "argo-cd.applicationSet.fullname" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.applicationSet.name "name" .Values.applicationSet.name) | nindent 4 }}
    {{- with .Values.applicationSet.pdb.labels }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.applicationSet.pdb.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ $value | quote }}
    {{- end }}
  {{- end }}
spec:
  {{- with .Values.applicationSet.pdb.maxUnavailable }}
  maxUnavailable: {{ . }}
  {{- else }}
  minAvailable: {{ .Values.applicationSet.pdb.minAvailable | default 0 }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "argo-cd.selectorLabels" (dict "context" . "name" .Values.applicationSet.name) | nindent 6 }}
{{- end }}
