{{- if .Values.repoServer.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.repoServer.serviceAccount.automountServiceAccountToken }}
metadata:
  name: {{ template "argo-cd.repoServerServiceAccountName" . }}
{{- if .Values.repoServer.serviceAccount.annotations }}
  annotations:
  {{- range $key, $value := .Values.repoServer.serviceAccount.annotations }}
    {{ $key }}: {{ $value | quote }}
  {{- end }}
{{- end }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.repoServer.name "name" .Values.repoServer.name) | nindent 4 }}
  {{- range $key, $value := .Values.repoServer.serviceAccount.labels }}
    {{ $key }}: {{ $value | quote }}
  {{- end }}
{{- end }}
