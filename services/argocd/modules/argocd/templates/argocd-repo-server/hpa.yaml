{{- if .Values.repoServer.autoscaling.enabled }}
apiVersion: {{ include "argo-cd.apiVersion.autoscaling" . }}
kind: HorizontalPodAutoscaler
metadata:
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.repoServer.name "name" (printf "%s-hpa" .Values.repoServer.name)) | nindent 4 }}
  name: {{ template "argo-cd.repoServer.fullname" . }}-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ template "argo-cd.repoServer.fullname" . }}
  minReplicas: {{ .Values.repoServer.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.repoServer.autoscaling.maxReplicas }}
  metrics:
  {{- with .Values.repoServer.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        {{- if eq (include "argo-cd.apiVersion.autoscaling" $) "autoscaling/v2beta1" }}
        targetAverageUtilization: {{ . }}
        {{- else }}
        target:
          averageUtilization: {{ . }}
          type: Utilization
        {{- end }}
  {{- end }}
  {{- with .Values.repoServer.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        {{- if eq (include "argo-cd.apiVersion.autoscaling" $) "autoscaling/v2beta1" }}
        targetAverageUtilization: {{ . }}
        {{- else }}
        target:
          averageUtilization: {{ . }}
          type: Utilization
        {{- end }}
  {{- end }}
  {{- with .Values.repoServer.autoscaling.behavior }}
  behavior:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
