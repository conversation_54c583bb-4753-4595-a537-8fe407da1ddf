{{- $config := .Values.repoServer.clusterAdminAccess | default dict -}}
{{- if hasKey $config "enabled" | ternary $config.enabled .Values.createClusterRoles }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "argo-cd.repoServer.fullname" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.repoServer.name "name" .Values.repoServer.name) | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "argo-cd.repoServer.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ include "argo-cd.repoServerServiceAccountName" . }}
  namespace: {{ .Release.Namespace }}
{{- end }}
