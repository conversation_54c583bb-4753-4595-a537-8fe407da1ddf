{{- if .Values.repoServer.serviceAccount.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ template "argo-cd.repoServer.fullname" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.repoServer.name "name" .Values.repoServer.name) | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ template "argo-cd.repoServer.fullname" . }}
subjects:
- kind: ServiceAccount
  name: {{ template "argo-cd.repoServerServiceAccountName" . }}
  namespace: {{ .Release.Namespace }}
{{- end }}