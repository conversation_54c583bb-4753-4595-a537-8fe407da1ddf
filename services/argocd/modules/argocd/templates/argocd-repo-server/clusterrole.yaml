{{- $config := .Values.repoServer.clusterAdminAccess | default dict -}}
{{- if hasKey $config "enabled" | ternary $config.enabled .Values.createClusterRoles }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "argo-cd.repoServer.fullname" . }}
  labels:
    {{- include "argo-cd.labels" (dict "context" . "component" .Values.repoServer.name "name" .Values.repoServer.name) | nindent 4 }}
rules:
  {{- if .Values.repoServer.clusterRoleRules.enabled }}
    {{- toYaml .Values.repoServer.clusterRoleRules.rules | nindent 2 }}
  {{- else }}
  - apiGroups:
    - '*'
    resources:
    - '*'
    verbs:
    - '*'
  - nonResourceURLs:
    - '*'
    verbs:
    - '*'
  {{- end }}
{{- end }}
