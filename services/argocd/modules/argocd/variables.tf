variable "kubernetes_config_path" {
  description = "The path to the kubernetes config"
  type        = string
  default     = "~/.kube/config"
}

variable "chart" {
  description = "The chart name to be installed"
  type        = string
  default     = "argocd"
}

variable "release_name" {
  description = "The release name of the application"
  type        = string
  default     = "argocd"
}

variable "namespace" {
  description = "The namespace to install the release into"
  type        = string
  default     = "argocd"
}

variable "create_namespace" {
  description = "Create the namespace if it does not yet exist"
  type        = bool
  default     = true
}
