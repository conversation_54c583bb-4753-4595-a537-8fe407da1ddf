apiVersion: v2
appVersion: v2.6.5
kubeVersion: ">=1.22.0-0"
description: A Helm chart for Argo CD, a declarative, GitOps continuous delivery tool for Kubernetes.
name: argo-cd
version: 5.26.2
home: https://github.com/argoproj/argo-helm
icon: https://argo-cd.readthedocs.io/en/stable/assets/logo.png
sources:
  - https://github.com/argoproj/argo-helm/tree/main/charts/argo-cd
  - https://github.com/argoproj/argo-cd
keywords:
  - argoproj
  - argocd
  - gitops
maintainers:
  - name: argoproj
    url: https://argoproj.github.io/
dependencies:
  - name: redis-ha
    version: 4.22.4
    repository: https://dandydeveloper.github.io/charts/
    condition: redis-ha.enabled
annotations:
  artifacthub.io/changes: |
    - kind: changed
      description: Use global.nodeSelector value as default value for nodeSelector on ApplicationSet
