module "aws_ecr_image" {
  source = "./modules/aws_ecr_image"

  region    = var.region
  tag       = var.ecr_image_tag
  role_name = var.eks_node_group_role_name
}

module "aws_kms_key" {
  source = "./modules/aws_kms_key"

  region    = var.region
  role_name = var.eks_node_group_role_name
}

module "argocd" {
  source = "./modules/argocd"

  kubernetes_config_path = var.kubernetes_config_path

  depends_on = [
    module.aws_ecr_image,
    module.aws_kms_key
  ]
}
