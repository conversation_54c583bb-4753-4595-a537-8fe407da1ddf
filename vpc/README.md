# Simple VPC

Configuration in this directory creates set of VPC resources.

There is a public and private subnet created per availability zone in addition to single NAT Gateway shared between all 3 availability zones.


## Environment

This module expects you to set the AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY environment variable, in order to read the correct AWS  credentials to operate with.

```bash
export AWS_ACCESS_KEY_ID=<your access key ID>
export AWS_SECRET_ACCESS_KEY=<your secret access key>
```


## Pre-requisites required version
- Terraform  >= 1.0.0
- AWS >= 3.48 
- GOlang 1.15.6

## Outputs

| Name | Description |
|------|-------------|
| nat_public_ips | NAT gateways |
| private_subnets | Subnets |
| public_subnets | List of IDs of public subnets |
| vpc_cidr_block | CIDR blocks |
| vpc_id | VPC |


## Run Terratest for module_vpc.tf

The Go test script gets the subnet id and checks with aws if it was successfully created.


In the tests folder run the following:

```bash
$ cd test
$ go mod init test
$ go mod tidy
$ go test -v
```

## Reference

If you want to have more details information about the module, check the follow links:

https://github.com/gruntwork-io/terragrunt

https://github.com/gruntwork-io/terratest

https://github.com/terraform-aws-modules/terraform-aws-vpc

https://registry.terraform.io/modules/terraform-aws-modules/vpc/aws/1.46.0
