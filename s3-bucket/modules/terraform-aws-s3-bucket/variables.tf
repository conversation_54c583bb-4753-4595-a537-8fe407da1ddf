variable "attach_elb_log_delivery_policy" {
  description = "Controls if S3 bucket should have ELB log delivery policy attached"
  type        = bool
  default     = false
}

variable "attach_deny_insecure_transport_policy" {
  description = "Controls if S3 bucket should have deny non-SSL transport policy attached"
  type        = bool
  default     = false
}

variable "attach_policy" {
  description = "Controls if S3 bucket should have bucket policy attached (set to `true` to use value of `policy` as bucket policy)"
  type        = bool
  default     = false
}

variable "bucket" {
  description = "(Optional, Forces new resource) The name of the bucket. If omitted, Terraform will assign a random, unique name."
  type        = string
  default     = null
}

variable "bucket_prefix" {
  description = "(Optional, Forces new resource) Creates a unique bucket name beginning with the specified prefix. Conflicts with bucket."
  type        = string
  default     = null
}

variable "acl" {
  description = "(Optional) The canned ACL to apply. Defaults to 'private'. Conflicts with `access_control_policy`"
  type        = string
  default     = null
}

variable "policy" {
  description = "(Optional) A valid bucket policy JSON document. Note that if the policy document is not specific enough (but still valid), Terraform may view the policy as constantly changing in a terraform plan. In this case, please make sure you use the verbose/specific version of the policy. For more information about building AWS IAM policy documents with Terraform, see the AWS IAM Policy Document Guide."
  type        = string
  default     = null
}

variable "tags" {
  description = "(Optional) A mapping of tags to assign to the bucket."
  type        = map(string)
  default     = {}
}

variable "force_destroy" {
  description = "(Optional, Default:false ) A boolean that indicates all objects should be deleted from the bucket so that the bucket can be destroyed without error. These objects are not recoverable."
  type        = bool
  default     = false
}

variable "acceleration_status" {
  description = "(Optional) Sets the accelerate configuration of an existing bucket. Can be Enabled or Suspended."
  type        = string
  default     = "Suspended"
}

variable "sse_algorithm" {
  type        = string
  default     = "AES256"
  description = "The server-side encryption algorithm to use. Valid values are `AES256` and `aws:kms`"
}

variable "kms_master_key_arn" {
  type        = string
  default     = ""
  description = "The AWS KMS master key ARN used for the `SSE-KMS` encryption. This can only be used when you set the value of `sse_algorithm` as `aws:kms`. The default aws/s3 AWS KMS master key is used if this element is absent while the `sse_algorithm` is `aws:kms`"
}

variable "bucket_key_enabled" {
  type        = bool
  default     = false
  description = <<-EOT
  Set this to true to use Amazon S3 Bucket Keys for SSE-KMS, which reduce the cost of AWS KMS requests.
  For more information, see: https://docs.aws.amazon.com/AmazonS3/latest/userguide/bucket-key.html
  EOT
}

variable "request_payer" {
  description = "(Optional) Specifies who should bear the cost of Amazon S3 data transfer. Can be either BucketOwner or Requester. By default, the owner of the S3 bucket would incur the costs of any data transfer. See Requester Pays Buckets developer guide for more information."
  type        = string
  default     = "BucketOwner"
}

variable "website" {
  description = "Map containing static web-site hosting or redirect configuration."
  type        = any
  default     = {}
}


variable "website_enabled" {
  description = "Whether to enable website config for this bucket."
  type        = bool
  default     = false
}

variable "logging_enabled" {
  description = "Whether to enable logging for this bucket."
  type        = bool
  default     = false
}

variable "cors_rule" {
  description = "List of maps containing rules for Cross-Origin Resource Sharing."
  type        = any
  default     = []
}

variable "versioning" {
  description = "Map containing versioning configuration."
  type        = map(string)
  default     = {}
}

variable "mfa" {
  description = "(Optional, Required if versioning_configuration mfa_delete is enabled) The concatenation of the authentication device's serial number, a space, and the value that is displayed on your authentication device."
  type        = string
  default     = ""
}

variable "logging" {
  description = "Map containing access bucket logging configuration."
  type        = map(string)
  default     = {}
}

variable "lifecycle_configuration" {
  description = "List of maps containing configuration of object lifecycle management."
  type        = any
  default     = []
}

variable "replication_configuration_enabled" {
  description = "Whether or not to create `aws_s3_bucket_replication_configuration` resource."
  type        = bool
  default     = false
}

variable "replication_configuration" {
  description = "Map containing cross-region replication configuration."
  type        = any
  default     = []
}

variable "object_lock_configuration" {
  description = "Map containing S3 object locking configuration."
  type        = any
  default     = {}
}


variable "object_lock_enabled" {
  description = "Whether or not to enable object locking."
  type        = bool
  default     = false
}

variable "block_public_acls" {
  description = "Whether Amazon S3 should block public ACLs for this bucket."
  type        = bool
  default     = false
}

variable "block_public_policy" {
  description = "Whether Amazon S3 should block public bucket policies for this bucket."
  type        = bool
  default     = false
}

variable "ignore_public_acls" {
  description = "Whether Amazon S3 should ignore public ACLs for this bucket."
  type        = bool
  default     = false
}

variable "restrict_public_buckets" {
  description = "Whether Amazon S3 should restrict public bucket policies for this bucket."
  type        = bool
  default     = false
}

variable "create_multi_region_access" {
  type        = bool
  description = "Whether to create `aws_s3control_multi_region_access_point` resource or not?"
  default     = false
}

variable "create_lifecycle_configuration" {
  type        = bool
  description = "Whether to create  `aws_s3_bucket_lifecycle_configuration` resource or not?"
  default     = false
}

variable "create_server_side_encryption_configuration" {
  type        = bool
  description = "Whether to create  `aws_s3_bucket_server_side_encryption_configuration` resource or not?"
  default     = false
}

variable "create_cors_configuration" {
  type        = bool
  description = "Whether to create  `aws_s3_bucket_cors_configuration` resource or not?"
  default     = false
}
variable "create_object_lambda" {
  type        = bool
  description = "Whether to create `aws_s3control_object_lambda_access_point` resource or not?"
  default     = false
}

variable "aws_lambda_function_arn" {
  type        = string
  description = "The Amazon Resource Name (ARN) of the AWS Lambda function."
  default     = null
}

variable "access_control_policy" {
  type        = any
  description = "List of maps containing  configuration that sets the ACL permissions for an object per grantee (Optional, Conflicts with `acl`)"
  default     = []
}


variable "create_outpost_endpoint" {
  type        = bool
  description = "Whether to create `aws_s3outposts_endpoint` resource or not?"
  default     = false
}

variable "security_group_id" {
  type        = string
  description = "Identifier of the EC2 Security Group."
  default     = null
}

variable "subnet_id" {
  type        = string
  description = "Identifier of the EC2 Subnet."
  default     = null
}


variable "iam_role_arn" {
  type        = string
  description = "The Amazon Resource Name (ARN) of the IAM role that Amazon S3 assumes when creating the replication configuration."
  default     = null
}
