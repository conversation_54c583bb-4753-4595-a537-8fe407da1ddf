#tfsec:ignore:AWS002 tfsec:ignore:AWS017
resource "aws_s3_bucket" "this" {
  bucket        = var.bucket
  bucket_prefix = var.bucket_prefix
  tags          = var.tags
  force_destroy = var.force_destroy

  dynamic "object_lock_configuration" {
    for_each = try(var.object_lock_enabled, false) == false ? [] : ["Enabled"]
    content {
      object_lock_enabled = object_lock_configuration.value
    }
  }
}

data "aws_canonical_user_id" "current" {}
data "aws_caller_identity" "current" {}
data "aws_partition" "current" {}
data "aws_outposts_outpost" "this" {
  count = var.create_outpost_endpoint ? 1 : 0
}


resource "aws_s3_bucket_acl" "this" {
  bucket = aws_s3_bucket.this.bucket
  acl    = var.acl

  dynamic "access_control_policy" {
    for_each = try(length(var.access_control_policy), 0) == 0 || try(length(var.acl), 0) > 0 ? [] : [1]
    content {
      dynamic "grant" {
        for_each = var.access_control_policy
        content {
          grantee {
            type = grant.value.type
            uri  = grant.value.uri
            id   = grant.value.id
          }
          permission = grant.value.permissions
        }
      }
      owner {
        id = data.aws_canonical_user_id.current.id
      }
    }
  }
}

resource "aws_s3_bucket_cors_configuration" "this" {
  count                 = var.create_cors_configuration ? 1 : 0
  bucket                = aws_s3_bucket.this.bucket
  expected_bucket_owner = data.aws_caller_identity.current.id
  dynamic "cors_rule" {
    for_each = var.cors_rule
    content {
      allowed_headers = lookup(cors_rule.value, "allowed_headers")
      allowed_methods = lookup(cors_rule.value, "allowed_methods", ["GET"])
      allowed_origins = lookup(cors_rule.value, "allowed_origins", ["*"])
      expose_headers  = lookup(cors_rule.value, "expose_headers")
      max_age_seconds = lookup(cors_rule.value, "max_age_seconds")
    }
  }
}
resource "aws_s3_bucket_accelerate_configuration" "this" {
  bucket = aws_s3_bucket.this.bucket
  status = var.acceleration_status
}

resource "aws_s3_bucket_request_payment_configuration" "this" {
  bucket = aws_s3_bucket.this.bucket
  payer  = var.request_payer
}

resource "aws_s3_bucket_website_configuration" "this" {
  count  = var.website_enabled ? 1 : 0
  bucket = aws_s3_bucket.this.bucket
  index_document {
    suffix = var.website.index_document
  }
  error_document {
    key = var.website.error_document
  }

  #This block is required only if other block arguments are not specified
  redirect_all_requests_to {
    host_name = var.website.host_name
    protocol  = var.website.protocol
  }
  dynamic "routing_rule" {
    for_each = var.website.routing_rule
    content {
      condition {
        http_error_code_returned_equals = lookup(routing_rule.value.condition, "http_error_code_returned_equals", 404)
        key_prefix_equals               = lookup(routing_rule.value.condition, "key_prefix_equals", null)
      }
      redirect {
        host_name               = lookup(routing_rule.value.redirect, "host_name", null)
        http_redirect_code      = lookup(routing_rule.value.redirect, "http_redirect_code", null)
        protocol                = lookup(routing_rule.value.redirect, "protocol", null)
        replace_key_prefix_with = lookup(routing_rule.value.redirect, "replace_key_prefix_with", null)
        replace_key_with        = lookup(routing_rule.value.redirect, "replace_key_with", null)
      }
    }
  }
}

resource "aws_s3_bucket_versioning" "this" {
  bucket                = aws_s3_bucket.this.bucket
  mfa                   = var.mfa
  expected_bucket_owner = data.aws_caller_identity.current.account_id

  versioning_configuration {
    status     = lookup(var.versioning, "status", "Enabled")
    mfa_delete = lookup(var.versioning, "mfa_delete", "Disabled")
  }
}

resource "aws_s3_bucket_logging" "this" {
  count         = var.logging_enabled ? 1 : 0
  bucket        = aws_s3_bucket.this.id
  target_bucket = var.logging.target_bucket
  target_prefix = var.logging.target_prefix

}

resource "aws_s3_bucket_object_lock_configuration" "this" {
  count                 = var.object_lock_enabled ? 1 : 0
  bucket                = aws_s3_bucket.this.bucket
  expected_bucket_owner = data.aws_caller_identity.current.account_id

  rule {
    default_retention {
      mode  = lookup(var.object_lock_configuration, "mode", "COMPLIANCE")
      days  = lookup(var.object_lock_configuration, "days", 30)
      years = lookup(var.object_lock_configuration, "years", null)

    }
  }

  token = lookup(var.object_lock_configuration, "token", null)
}

resource "aws_s3_bucket_lifecycle_configuration" "this" {
  # Must have bucket versioning enabled first
  depends_on = [aws_s3_bucket_versioning.this]
  bucket     = aws_s3_bucket.this.id
  count      = var.create_lifecycle_configuration ? 1 : 0

  dynamic "rule" {
    for_each = var.lifecycle_configuration == null ? [] : var.lifecycle_configuration

    content {
      id     = rule.value.id
      status = rule.value.status

      filter {
        dynamic "and" {
          for_each = rule.value.filter_and == null ? [] : [rule.value.filter_and]
          content {
            object_size_greater_than = and.value.object_size_greater_than
            object_size_less_than    = and.value.object_size_less_than
            prefix                   = and.value.prefix
            tags                     = and.value.tags
          }
        }
      }

      dynamic "abort_incomplete_multipart_upload" {
        for_each = rule.value.abort_incomplete_multipart_upload_days == null ? [] : [1]
        content {
          days_after_initiation = rule.value.abort_incomplete_multipart_upload_days
        }
      }

      dynamic "transition" {
        for_each = rule.value.transition
        content {
          storage_class = transition.value.storage_class
          days          = transition.value.days
          date          = transition.value.date
        }
      }

      dynamic "expiration" {
        for_each = rule.value.expiration == null ? [] : [rule.value.expiration]
        content {
          date                         = expiration.value.date
          days                         = expiration.value.days
          expired_object_delete_marker = expiration.value.expired_object_delete_marker
        }
      }

      dynamic "noncurrent_version_expiration" {
        for_each = rule.value.noncurrent_version_expiration
        content {
          newer_noncurrent_versions = expiration.value.newer_noncurrent_versions
          noncurrent_days           = expiration.value.noncurrent_days
        }
      }

      dynamic "noncurrent_version_transition" {
        for_each = rule.value.noncurrent_version_transition
        content {
          newer_noncurrent_versions = transition.value.newer_noncurrent_versions
          noncurrent_days           = transition.value.noncurrent_days
          storage_class             = transition.value.storage_class
        }
      }
    }
  }
}

resource "aws_s3_bucket_replication_configuration" "this" {
  # Must have bucket versioning enabled first
  depends_on = [aws_s3_bucket_versioning.this]
  count      = var.replication_configuration_enabled ? 1 : 0
  bucket     = aws_s3_bucket.this.id
  role       = var.iam_role_arn
  dynamic "rule" {
    for_each = var.replication_configuration == null ? [] : var.replication_configuration
    content {
      id       = rule.value.id
      status   = rule.value.status
      priority = lookup(rule.value, "priority", null)

      # If the corresponding rule requires no filter, an empty configuration block filter {} must be specified.
      dynamic "filter" {
        for_each = try(rule.value.filter, null) == null ? [{ prefix = null, tags = {} }] : [rule.value.filter]

        content {
          prefix = try(filter.value.prefix, try(rule.value.prefix, null))
          dynamic "tag" {
            for_each = try(filter.value.tags, {})

            content {
              key   = tag.key
              value = tag.value
            }
          }
        }
      }
      delete_marker_replication {
        status = lookup(rule.value, "delete_marker_replication_status", "Disabled")
      }

      destination {
        bucket        = rule.value.destination_bucket
        storage_class = lookup(rule.value.destination, "storage_class", "STANDARD")
        account       = lookup(rule.value.destination, "account", null)

        dynamic "encryption_configuration" {
          for_each = try(rule.value.destination.replica_kms_key_id, null) != null ? [1] : []

          content {
            replica_kms_key_id = try(rule.value.destination.replica_kms_key_id, null)
          }
        }

        # This block is used together with the account argument.
        dynamic "access_control_translation" {
          for_each = try(rule.value.destination.access_control_translation.owner, null) == null ? [] : [rule.value.destination.access_control_translation.owner]
          content {
            owner = access_control_translation.value
          }
        }

        dynamic "metrics" {
          for_each = try(rule.value.destination.metrics.status, "") == "Enabled" ? [1] : []

          content {
            status = "Enabled"
            event_threshold {
              # Minutes can only have 15 as a valid value.
              minutes = 15
            }
          }
        }

        # This block is required when replication metrics are enabled.
        dynamic "replication_time" {
          for_each = try(rule.value.destination.metrics.status, "") == "Enabled" ? [1] : []

          content {
            status = "Enabled"
            time {
              # Minutes can only have 15 as a valid value.
              minutes = 15
            }
          }
        }
      }

      dynamic "source_selection_criteria" {
        for_each = try(rule.value.source_selection_criteria.sse_kms_encrypted_objects.status, null) == null ? [] : [rule.value.source_selection_criteria.sse_kms_encrypted_objects.status]
        content {
          # This block is required when replica_kms_key_id is speicified.
          sse_kms_encrypted_objects {
            status = sse_kms_encrypted_objects.value
          }
        }
      }
    }
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "this" {
  bucket = aws_s3_bucket.this.bucket
  count  = var.create_server_side_encryption_configuration ? 1 : 0

  rule {
    bucket_key_enabled = var.bucket_key_enabled

    apply_server_side_encryption_by_default {
      sse_algorithm     = var.sse_algorithm
      kms_master_key_id = var.kms_master_key_arn
    }
  }
}

resource "aws_s3_bucket_policy" "this" {
  count = var.attach_policy ? 1 : 0

  bucket = aws_s3_bucket.this.id
  policy = data.aws_iam_policy_document.combined.json
}

data "aws_iam_policy_document" "combined" {
  source_policy_documents = compact([
    var.attach_deny_insecure_transport_policy ? data.aws_iam_policy_document.deny_insecure_transport.json : "",
    var.attach_elb_log_delivery_policy ? data.aws_iam_policy_document.elb_log_delivery.json : "",
    var.attach_policy ? var.policy : ""
  ])
}

data "aws_iam_policy_document" "deny_insecure_transport" {
  statement {
    sid    = "denyInsecureTransport"
    effect = "Deny"

    actions = [
      "s3:*",
    ]

    resources = [
      aws_s3_bucket.this.arn,
      "${aws_s3_bucket.this.arn}/*",
    ]

    principals {
      type        = "*"
      identifiers = ["*"]
    }

    condition {
      test     = "Bool"
      variable = "aws:SecureTransport"
      values = [
        "false"
      ]
    }
  }
}

data "aws_elb_service_account" "this" {
}

data "aws_iam_policy_document" "elb_log_delivery" {
  statement {
    sid = ""

    principals {
      type        = "AWS"
      identifiers = data.aws_elb_service_account.this.*.arn
    }

    effect = "Allow"

    actions = [
      "s3:PutObject",
    ]

    resources = [
      "${aws_s3_bucket.this.arn}/*",
    ]
  }
}


resource "aws_s3_access_point" "this" {
  bucket = aws_s3_bucket.this.id
  name   = var.bucket
  public_access_block_configuration {
    block_public_acls       = var.block_public_acls
    block_public_policy     = var.block_public_policy
    ignore_public_acls      = var.ignore_public_acls
    restrict_public_buckets = var.restrict_public_buckets
  }

  policy = ""
  depends_on = [
    aws_s3_bucket_policy.this
  ]
}

resource "aws_s3control_multi_region_access_point" "this" {
  count = var.create_multi_region_access ? 1 : 0
  details {
    name = var.bucket

    public_access_block {
      block_public_acls       = var.block_public_acls
      block_public_policy     = var.block_public_policy
      ignore_public_acls      = var.ignore_public_acls
      restrict_public_buckets = var.restrict_public_buckets
    }
    region {
      bucket = aws_s3_bucket.this.id
    }
  }
}

resource "aws_s3control_multi_region_access_point_policy" "this" {
  count = var.create_multi_region_access ? 1 : 0
  details {
    name = element(split(":", aws_s3control_multi_region_access_point.this[0].id), 1)
    policy = jsonencode({
      "Version" : "2012-10-17",
      "Statement" : [
        {
          "Sid" : "Example",
          "Effect" : "Allow",
          "Principal" : {
            "AWS" : data.aws_caller_identity.current.account_id
          },
          "Action" : ["s3:GetObject", "s3:PutObject"],
          "Resource" : "arn:${data.aws_partition.current.partition}:s3::${data.aws_caller_identity.current.account_id}:accesspoint/${aws_s3control_multi_region_access_point.this[0].alias}/object/*"
        }
      ]
    })
  }
}

resource "aws_s3control_object_lambda_access_point" "this" {
  count      = var.create_object_lambda ? 1 : 0
  name       = var.bucket
  account_id = data.aws_caller_identity.current.account_id

  configuration {
    supporting_access_point = aws_s3_access_point.this.arn

    transformation_configuration {
      actions = ["GetObject"]

      content_transformation {
        aws_lambda {
          function_arn = var.aws_lambda_function_arn
        }
      }
    }
  }
}

resource "aws_s3control_object_lambda_access_point_policy" "this" {
  count = var.create_object_lambda ? 1 : 0
  name  = aws_s3control_object_lambda_access_point.this.*.name

  policy = jsonencode({
    Version = "2008-10-17"
    Statement = [{
      Effect = "Allow"
      Action = "s3-object-lambda:GetObject"
      Principal = {
        AWS = data.aws_caller_identity.current.account_id
      }
      Resource = aws_s3control_object_lambda_access_point.this.*.arn
    }]
  })
}

resource "aws_s3outposts_endpoint" "this" {
  count             = var.create_outpost_endpoint ? 1 : 0
  outpost_id        = data.aws_outposts_outpost.this.*.id
  security_group_id = var.security_group_id
  subnet_id         = var.subnet_id
}
