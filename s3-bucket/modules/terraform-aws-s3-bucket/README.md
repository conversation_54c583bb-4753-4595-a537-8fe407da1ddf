# S3 Bucket

Creates S3 bucket with different configurations.

<!-- BEGINNING OF PRE-COMMIT-TERRAFORM DOCS HOOK -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.0.1 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 4.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 4.0 |

## Modules

No modules.

## Resources

| Name | Type |
|------|------|
| [aws_s3_bucket](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_object) | resource |
| [aws_s3_bucket_acl](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_acl) | resource |
| [aws_s3_bucket_cors_configuration](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_cors_configuration) | resource |
| [aws_s3_bucket_accelerate_configuration](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_accelerate_configuration) | resource |
| [aws_s3_bucket_request_payment_configuration](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_request_payment_configuration) | resource |
| [aws_s3_bucket_website_configuration](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_website_configuration) | resource |
| [aws_s3_bucket_versioning](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_logging) | resource |
| [aws_s3_bucket_logging](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_object) | resource |
| [aws_s3_bucket_object_lock_configuration](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_object_lock_configuration) | resource |
| [aws_s3_bucket_lifecycle_configuration](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_lifecycle_configuration) | resource |
| [aws_s3_bucket_replication_configuration](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_object) | resource |
| [aws_s3_bucket_server_side_encryption_configuration](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_server_side_encryption_configuration) | resource |
| [aws_s3_bucket_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket_policy) | resource |
| [aws_s3_access_point](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_access_point) | resource |
| [aws_s3control_multi_region_access_point](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3control_multi_region_access_point) | resource |
| [aws_s3control_multi_region_access_point_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3control_multi_region_access_point_policy) | resource |
| [aws_s3control_object_lambda_access_point](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3control_object_lambda_access_point) | resource |
| [aws_s3control_object_lambda_access_point_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3control_object_lambda_access_point_policy) | resource |
| [aws_s3outposts_endpoint](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3outposts_endpoint) | resource |
## Parameters or Configuration

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| **acceleration_status** |(Optional) Sets the accelerate configuration of an existing bucket. Can be Enabled or Suspended. | `string` | `Enabled` | no |
| **access_control_policy** | List of maps containing  configuration that sets the ACL permissions for an object per grantee (Optional, Conflicts with `acl`). | `any` | `[]` | no |
| **acl** | (Optional) The canned ACL to apply. Defaults to 'private'. Conflicts with `access_control_policy`. | `string` | `null` | no |
| **attach_deny_insecure_transport_policy**| Controls if S3 bucket should have deny non-SSL transport policy attached. | `bool` | `false` | no |
| **attach_elb_log_delivery_policy** | Controls if S3 bucket should have ELB log delivery policy attached. | `bool` | `false` | no |
| **attach_policy** | Controls if S3 bucket should have bucket policy attached (set to `true` to use value of `policy` as bucket policy). | `bool` | `false` | no |
| **aws_lambda_function_arn** | The Amazon Resource Name (ARN) of the AWS Lambda function. | `string` | `null` | no |
| **block_public_acls** | Whether Amazon S3 should block public ACLs for this bucket. | `bool` | `false` | no |
| **block_public_policy** | Whether Amazon S3 should block public bucket policies for this bucket. | `bool` | `false` | no |
| **bucket** | (Optional, Forces new resource) The name of the bucket. If omitted, Terraform will assign a random, unique name. | `string` | `null` | no |
| **bucket_key_enabled** | Set this to true to use Amazon S3 Bucket Keys for SSE-KMS, which reduce the cost of AWS KMS requests. For more information, see: https://docs.aws.amazon.com/AmazonS3/latest/userguide/bucket-key.html | `bool` | `false` | no |
| **bucket_prefix** | (Optional, Forces new resource) Creates a unique bucket name beginning with the specified prefix. Conflicts with bucket.. | `string` | `null` | no |
| **cors_rule** | List of maps containing rules for Cross-Origin Resource Sharing. | `any` | `[{}]` | no |
| **create_cors_configuration** | Whether to create  `aws_s3_bucket_cors_configuration` resource or not?. | `bool` | `false` | no |
| **create_lifecycle_configuration** | Whether to create  `aws_s3_bucket_lifecycle_configuration` resource or not? | `bool` | `false` | no |
| **create_multi_region_access** | Whether to create  `aws_s3control_multi_region_access_point` resource or not? | `bool` | `false` | no |
| **create_object_lambda** | Whether to create `aws_s3control_object_lambda_access_point` resource or not? | `bool` | `false` | no |
| **create_outpost_endpoint** | Whether to create `aws_s3outposts_endpoint` resource or not? | `bool` | `false` | no |
| **create_server_side_encryption_configuration** | Whether to create  `aws_s3_bucket_server_side_encryption_configuration` resource or not? | `bool` | `false` | no |
| **force_destroy** | (Optional, Default:false ) A boolean that indicates all objects should be deleted from the bucket so that the bucket can be destroyed without error. These objects are not recoverable. | `bool` | `false` | no |
| **iam_role_arn** | The Amazon Resource Name (ARN) of the IAM role that Amazon S3 assumes when creating the replication configuration. | `string` | `null` | no |
| **ignore_public_acls** | Whether Amazon S3 should ignore public ACLs for this bucket. | `bool` | `false` | no |
| **kms_master_key_arn** |The AWS KMS master key ARN used for the `SSE-KMS` encryption. This can only be used when you set the value of `sse_algorithm` as `aws:kms`. The default aws/s3 AWS KMS master key is used if this element is absent while the `sse_algorithm` is `aws:kms`. |`string`|`""`|no|
| **lifecycle_rule** | "List of maps containing configuration of object lifecycle management. | `any` | `null` | no |
| **logging** | Map containing access bucket logging configuration. | `map(string)` | `{}` | no |
| **logging_enabled** | Whether to enable logging for this bucket. | `bool` | `false` | no |
| **mfa** | (Optional, Required if versioning_configuration mfa_delete is enabled) The concatenation of the authentication device's serial number, a space, and the value that is displayed on your authentication device. | `string` | `""` | no |
| **object_lock_configuration** | Map containing S3 object locking configuration. | `map(string)` | `{}` | no |
| **object_lock_enabled** | Whether or not to enable object locking. | `bool` | `false` | no |
| **policy** | (Optional) A valid bucket policy JSON document. Note that if the policy document is not specific enough (but still valid), Terraform may view the policy as constantly changing in a terraform plan. In this case, please make sure you use the verbose/specific version of the policy. For more information about building AWS IAM policy documents with Terraform, see the AWS IAM Policy Document Guide. | `string` | `null` | no |
| **replication_configuration** | Map containing cross-region replication configuration. | `any` | `{}` | no |
| **replication_configuration_enabled** | Whether or not to create `aws_s3_bucket_replication_configuration` resource. | `bool` | `false` | no |
| **request_payer** | (Optional) Specifies who should bear the cost of Amazon S3 data transfer. Can be either BucketOwner or Requester. By default, the owner of the S3 bucket would incur the costs of any data transfer. See Requester Pays Buckets developer guide for more information. | `string` | `BucketOwner` | no |
| **restrict_public_buckets** | Whether Amazon S3 should restrict public bucket policies for this bucket. | `bool` | `false` | no |
| **security_group_id** | Identifier of the EC2 Security Group. | `string` | `null` | no |
| **server_side_encryption_configuration** | Map containing server-side encryption configuration. | `any` | `{}` | no |
| **sse_algorithm** | The server-side encryption algorithm to use. Valid values are `AES256` and `aws:kms`. | `string` | `AES256` | no |
| **subnet_id** | Identifier of the EC2 Subnet. | `string`| `null` | no |
| **tags** | (Optional) A mapping of tags to assign to the bucket. | `map(string)` | `{}` | no |
| **website** | Map containing static web-site hosting or redirect configuration. | `map(string)` | `{}` | no |
| **websit_enabled** | Whether to enable website config for this bucket. | `bool` | `false` | no |

## Outputs

| Name | Description |
|------|-------------|
| **this_s3_bucket_id** | The name of the bucket. |
| **this_s3_bucket_arn** | The ARN of the bucket. Will be of format arn:aws:s3:::bucketname.|
| **this_s3_bucket_bucket_domain_name** | The bucket domain name. Will be of format bucketname.s3.amazonaws.com. |
| **this_s3_bucket_bucket_regional_domain_name** | The bucket region-specific domain name. The bucket domain name including the region name, please refer here for format. Note: The AWS CloudFront allows specifying S3 region-specific endpoint when creating S3 origin, it will prevent redirect issues from CloudFront to S3 Origin URL. |
| **this_s3_bucket_hosted_zone_id** | The Route 53 Hosted Zone ID for this bucket's region. |
| **this_s3_bucket_region** | The AWS region this bucket resides in. |
| **this_s3_bucket_website_endpoint** | The website endpoint, if the bucket is configured with a website. If not, this will be an empty string. |
| **this_s3_bucket_website_domain** | The domain of the website endpoint, if the bucket is configured with a website. If not, this will be an empty string. This is used to create Route 53 alias records. |

## Schema Example
- Schema for `lifecycle_configuration`
```hcl
[
  {
    enabled = true # bool
    id      = string
    abort_incomplete_multipart_upload_days = null # number
    filter_and = {
      object_size_greater_than = null # integer >= 0
      object_size_less_than    = null # integer >= 1
      prefix                   = null # string
      tags                     = {}   # map(string)
    }
    expiration = {
      date                         = null # string, RFC3339 time format, GMT
      days                         = null # integer > 0
      expired_object_delete_marker = null # bool
    }
    noncurrent_version_expiration = {
      newer_noncurrent_versions = null # integer > 0
      noncurrent_days           = null # integer >= 0
    }
    transition = [{
      date          = null # string, RFC3339 time format, GMT
      days          = null # integer >= 0
      storage_class = null # string/enum, one of GLACIER, STANDARD_IA, ONEZONE_IA, INTELLIGENT_TIERING, DEEP_ARCHIVE, GLACIER_IR.
    }]
    noncurrent_version_transition = [{
      newer_noncurrent_versions = null # integer >= 0
      noncurrent_days           = null # integer >= 0
      storage_class             = null # string/enum, one of GLACIER, STANDARD_IA, ONEZONE_IA, INTELLIGENT_TIERING, DEEP_ARCHIVE, GLACIER_IR.
    }]
  }
]
```
- Schema for `replication_configuration`
```hcl
[
  {
      id                               = string
      priority                         = number
      prefix                           = string
      status                           = string
      delete_marker_replication_status = string

      destination_bucket = string

      destination = {
        storage_class      = string
        replica_kms_key_id = string
        access_control_translation = {
          owner = string
        }
        account = string
        metrics = {
          status = string
        }
      }
      source_selection_criteria = {
        sse_kms_encrypted_objects = {
          status = string
        }
      }

      filter = {
        prefix = string
        tags   = map(string)
      }
    }
  ]
```
- Schema for `cors_rule`
```hcl
[
  {
    allowed_headers = list(string)
    allowed_methods = list(string)
    allowed_origins = list(string)
    expose_headers  = list(string)
    max_age_seconds = number
  }
]
```

- Schema for `access_control_policy`
```hcl
[
    {
      id          = string
      type        = string
      permissions = list(string)
      uri         = string
    }
[
```
<!-- END OF PRE-COMMIT-TERRAFORM DOCS HOOK -->
