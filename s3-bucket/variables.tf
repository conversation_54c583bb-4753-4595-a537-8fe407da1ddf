variable "region" {
  description = "AWS Region"
  type        = string
  default     = "us-east-1"
}

variable "extra_tags" {
  description = "Map of extra tags to append to the resulting bucket"
  type        = map(string)
  default     = {}
}

variable "bucket" {
  type        = string
  description = "The name of the bucket. If omitted, Terraform will assign a random, unique name"
}

variable "acl" {
  type        = string
  description = "ACL to apply"
  default     = "private"
}

variable "attach_policy" {
  type        = bool
  description = "Should we attach a policy?"
  default     = false
}

variable "policy" {
  type        = string
  description = "Policy to apply, should set attach_policy to true also"
  default     = ""
}

variable "force_destroy" {
  description = "(Optional, Default:false ) A boolean that indicates all objects should be deleted from the bucket so that the bucket can be destroyed without error. These objects are not recoverable."
  type        = bool
  default     = false
}

# object vars
variable "create_object" {
  type        = bool
  description = "Create object"
  default     = false
}

variable "object_key" {
  type        = string
  description = "Name of the object once it is in the bucket."
  default     = null
}

variable "object_acl" {
  type        = string
  description = "ACL to apply"
  default     = null
}

variable "block_public_acls" {
  description = "Whether Amazon S3 should block public ACLs for this bucket."
  type        = bool
  default     = true
}

variable "block_public_policy" {
  description = "Whether Amazon S3 should block public bucket policies for this bucket."
  type        = bool
  default     = true
}

variable "ignore_public_acls" {
  description = "Whether Amazon S3 should ignore public ACLs for this bucket."
  type        = bool
  default     = true
}

variable "restrict_public_buckets" {
  description = "Whether Amazon S3 should restrict public bucket policies for this bucket."
  type        = bool
  default     = true
}

variable "acceleration_status" {
  description = "(Optional) Sets the accelerate configuration of an existing bucket. Can be Enabled or Suspended."
  type        = string
  default     = "Suspended"
}
