provider "aws" {
  region = var.region
}

module "bucket" {
  source = "./modules/terraform-aws-s3-bucket"

  bucket                  = var.bucket
  acl                     = var.acl
  attach_policy           = var.attach_policy
  policy                  = var.policy
  block_public_acls       = var.block_public_acls
  block_public_policy     = var.block_public_policy
  ignore_public_acls      = var.ignore_public_acls
  restrict_public_buckets = var.restrict_public_buckets
  force_destroy           = var.force_destroy
  tags = merge(
    {
      "Terraform" = "true"
    },
    var.extra_tags
  )
}

module "object" {
  count = var.create_object ? 1 : 0

  source = "./modules/terraform-aws-s3-bucket/modules/object"

  bucket = module.bucket.this_s3_bucket_id

  key = var.object_key
  acl = var.object_acl
}
