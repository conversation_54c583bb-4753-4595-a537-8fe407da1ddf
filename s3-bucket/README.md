# S3-Bucket Module

## Description

This is a terraform module to set up a S3 Bucket in AWS.
It also has the option to create objects inside the bucket.

## Requirements

There are a few requirements for this project:

|    | Terraform |
|:--:|-----------|
| >= |   1.0.1   |

|    |    AWS    |
|:--:|-----------|
| ~> |  4.0  |

## Usage

To install this module you should run:

```
terraform init
```

To create only a bucket, the bucket name is required:

```
terraform apply -var bucket=<bucket_name>
```

To create an object in the bucket, you need to also set the `create_object` to true and the `object_key`:

```
tf apply -var bucket=<bucket_name> -var create_object=true -var object_key=<object_key>
```



To check if your deployment is running:

```
aws s3 ls s3://<BUCKET NAME>
```

## Parameters or Configuration

In the `variables.tf` file we can explicit declare:

|               | Default Value |     Type    |                    Description                    |
|:-------------:|:-------------:|:-----------:|:-------------------------------------------------:|
|     **bucket**    |       ""      |    string   |  Name of the bucket  |
|      **acl**      |   "private"   |    string   |  ACL to apply to the bucket  |
| **extra_tags** | {} | map(string) | Map of extra tags to append to the resulting bucket |
| **attach_policy** | false | bool | Should we attach a policy? |
| **policy** | "" | string | Policy to apply, should set attach_policy to true also |

Also there are some variables for the object creation:

|               | Default Value |     Type    |                    Description                    |
|:-------------:|:-------------:|:-----------:|:-------------------------------------------------:|
|     **create_object**    |       false      |    bool  |  Should we create an object?  |
|      **object_source**      |   null   |    string   |   Path to a file that will be read and uploaded as raw bytes for the object content.   |
|      **object_content**      |   null   |    string   |  Literal string value to use as the object content, which will be uploaded as UTF-8-encoded text   |
|      **object_key**      |   null   |    string   |   Name of the object once in the bucket   |
|      **object_acl**      |   null   |    string   |   ACL to apply to the object   |

## Repository Organization

* modules/terraform-aws-s3-bucket: contains the code for the terraform module
* main.tf: contains the configuration for the module

## Testing

In order to test this repository you must first install all dependencies:

``` 
cd test
go get -v -d -t ./... 
```

Then, you can execute the tests, from inside the `test` folder:

```
go test -v -timeout 10m
```
